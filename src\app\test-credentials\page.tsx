"use client";

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, Shield, Search, User } from 'lucide-react';

interface TestUser {
  email: string;
  password: string;
  role: string;
  name: string;
  company: string;
  description: string;
}

const testUsers: TestUser[] = [
  // TechCorp Industries
  {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    name: '<PERSON>',
    company: 'TechCorp Industries',
    description: 'Company Administrator - Can manage all reports and users'
  },
  {
    email: '<EMAIL>',
    password: 'investigator123',
    role: 'investigator',
    name: '<PERSON>',
    company: 'TechCorp Industries',
    description: 'Lead Investigator - Handles report investigations'
  },
  {
    email: '<EMAIL>',
    password: 'whistleblower123',
    role: 'whistleblower',
    name: '<PERSON>',
    company: 'TechCorp Industries',
    description: 'Employee - Can submit and track reports'
  },
  {
    email: '<EMAIL>',
    password: 'whistleblower123',
    role: 'whistleblower',
    name: 'Jane Smith',
    company: 'TechCorp Industries',
    description: 'Employee - Can submit and track reports'
  },
  // Global Manufacturing
  {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    name: 'Emily Rodriguez',
    company: 'Global Manufacturing Ltd',
    description: 'Company Administrator - Can manage all reports and users'
  },
  {
    email: '<EMAIL>',
    password: 'investigator123',
    role: 'investigator',
    name: 'David Thompson',
    company: 'Global Manufacturing Ltd',
    description: 'Lead Investigator - Handles report investigations'
  },
  {
    email: '<EMAIL>',
    password: 'whistleblower123',
    role: 'whistleblower',
    name: 'Robert Wilson',
    company: 'Global Manufacturing Ltd',
    description: 'Factory Worker - Can submit and track reports'
  },
  {
    email: '<EMAIL>',
    password: 'whistleblower123',
    role: 'whistleblower',
    name: 'Lisa Brown',
    company: 'Global Manufacturing Ltd',
    description: 'HR Employee - Can submit and track reports'
  }
];

export default function TestCredentialsPage() {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [message, setMessage] = useState('');
  const { login, user, logout } = useAuth();

  const handleTestLogin = async (testUser: TestUser) => {
    setIsLoading(testUser.email);
    setMessage('');
    
    try {
      const success = await login(testUser.email, testUser.password, testUser.role as 'admin' | 'whistleblower');
      if (success) {
        setMessage(`✅ Login successful as ${testUser.name}! Redirecting to dashboard...`);
      } else {
        setMessage('❌ Login failed - Invalid credentials');
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setMessage(`❌ Login error: ${errorMessage}`);
    } finally {
      setIsLoading(null);
    }
  };

  const handleLogout = async () => {
    await logout();
    setMessage('🔓 Logged out successfully');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="w-4 h-4" />;
      case 'investigator':
        return <Search className="w-4 h-4" />;
      case 'whistleblower':
        return <User className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'investigator':
        return 'bg-blue-100 text-blue-800';
      case 'whistleblower':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCompanyColor = (company: string) => {
    return company.includes('TechCorp') 
      ? 'bg-blue-50 border-blue-200' 
      : 'bg-green-50 border-green-200';
  };

  const groupedUsers = testUsers.reduce((acc, user) => {
    if (!acc[user.company]) {
      acc[user.company] = [];
    }
    acc[user.company].push(user);
    return acc;
  }, {} as Record<string, TestUser[]>);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">Test Credentials</h1>
          <p className="text-gray-600">
            Use these credentials to test different user roles and company access
          </p>
        </div>

        {/* Current User Status */}
        {user ? (
          <Card className="bg-green-50 border-green-200">
            <CardHeader>
              <CardTitle className="text-green-800 flex items-center gap-2">
                <User className="w-5 h-5" />
                Currently Logged In
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Name:</strong> {user.firstName} {user.lastName}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Role:</strong> {user.role}</p>
                <p><strong>Company:</strong> {user.companyId || 'N/A'}</p>
                <Button onClick={handleLogout} variant="outline" className="mt-2">
                  Logout
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-gray-50 border-gray-200">
            <CardContent className="pt-6">
              <p className="text-gray-600 text-center">No user currently logged in</p>
            </CardContent>
          </Card>
        )}

        {/* Status Message */}
        {message && (
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-6">
              <p className="text-blue-800 text-center">{message}</p>
            </CardContent>
          </Card>
        )}

        {/* Test Users by Company */}
        {Object.entries(groupedUsers).map(([company, users]) => (
          <Card key={company} className={getCompanyColor(company)}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="w-5 h-5" />
                {company}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {users.map((testUser) => (
                  <Card key={testUser.email} className="bg-white">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{testUser.name}</CardTitle>
                        <Badge className={getRoleColor(testUser.role)}>
                          <div className="flex items-center gap-1">
                            {getRoleIcon(testUser.role)}
                            {testUser.role}
                          </div>
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <p className="text-sm text-gray-600">{testUser.description}</p>
                      <div className="space-y-1 text-sm">
                        <p><strong>Email:</strong> {testUser.email}</p>
                        <p><strong>Password:</strong> {testUser.password}</p>
                      </div>
                      <Button
                        onClick={() => handleTestLogin(testUser)}
                        disabled={isLoading === testUser.email}
                        className="w-full"
                        size="sm"
                      >
                        {isLoading === testUser.email ? 'Logging in...' : 'Test Login'}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Database Actions */}
        <Card className="bg-yellow-50 border-yellow-200">
          <CardHeader>
            <CardTitle className="text-yellow-800">Database Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-yellow-700">
                Use these actions to manage the test database:
              </p>
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => window.open('/api/seed-refined', '_blank')}
                  variant="outline"
                  size="sm"
                >
                  Reseed Database
                </Button>
                <Button
                  onClick={() => window.open('/api/dashboard/stats', '_blank')}
                  variant="outline"
                  size="sm"
                >
                  View Stats API
                </Button>
                <Button
                  onClick={() => window.open('/api/reports', '_blank')}
                  variant="outline"
                  size="sm"
                >
                  View Reports API
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Workflow Information */}
        <Card className="bg-purple-50 border-purple-200">
          <CardHeader>
            <CardTitle className="text-purple-800">Workflow Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-purple-700">
              <p><strong>Current Data Structure:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>2 Companies: TechCorp Industries & Global Manufacturing Ltd</li>
                <li>8 Users: 2 Admins, 2 Investigators, 4 Whistleblowers</li>
                <li>4 Reports: Each with conversations and messages</li>
                <li>Real-time messaging between users</li>
                <li>Company-specific data isolation</li>
                <li>Automatic admin confirmations for new reports</li>
                <li>Live stats updates and notifications</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}