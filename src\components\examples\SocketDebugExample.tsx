'use client';

import { useSocket } from '@/hooks/useSocket';
import { SocketDebugPanel } from '@/components/debug/SocketDebugPanel';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface SocketDebugExampleProps {
  userId: string;
  userRole: string;
}

/**
 * Example component showing how to integrate Socket Debug Panel
 * 
 * Usage:
 * 1. Import this component in your dashboard or messaging interface
 * 2. Pass the current user's ID and role
 * 3. The debug panel will appear as a floating widget in bottom-right
 * 
 * To use in your component:
 * ```tsx
 * import { SocketDebugExample } from '@/components/examples/SocketDebugExample';
 * 
 * // In your component:
 * {process.env.NODE_ENV === 'development' && (
 *   <SocketDebugExample userId={user.id} userRole={user.role} />
 * )}
 * ```
 */
export function SocketDebugExample({ userId, userRole }: SocketDebugExampleProps) {
  const socketData = useSocket(
    userId,
    userRole,
    (message) => {
      console.log('📨 New message received:', message);
    },
    (typingData) => {
      console.log('⌨️ Typing indicator:', typingData);
    },
    (statusChange) => {
      console.log('👤 User status change:', statusChange);
    }
  );

  return (
    <div className="p-4">
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Socket Connection Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Status Display */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                <span className={`font-medium ${socketData.isConnected ? 'text-green-600' : 'text-red-600'}`}>
                  {socketData.isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Health:</span>
                <span className={`font-medium ${socketData.connectionHealth.isStable ? 'text-green-600' : 'text-yellow-600'}`}>
                  {socketData.connectionHealth.isStable ? 'Stable' : 'Unstable'}
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Reconnections:</span>
                <span className="font-medium">{socketData.connectionHealth.reconnectionCount}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Online Users:</span>
                <span className="font-medium">{socketData.onlineUsers.size}</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                console.log('🔍 Full Socket Status:', {
                  connection: socketData.isConnected,
                  health: socketData.connectionHealth,
                  onlineUsers: Array.from(socketData.onlineUsers),
                  typingUsers: Object.fromEntries(socketData.typingUsers)
                });
              }}
            >
              Log Status
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const report = socketData.connectionHealth.getHealthReport();
                alert(`Health: ${report.status}\n\nIssues:\n${report.issues.join('\n') || 'None'}`);
              }}
            >
              Health Report
            </Button>
          </div>

          {/* Health Issues Warning */}
          {(() => {
            const report = socketData.connectionHealth.getHealthReport();
            if (report.status !== 'healthy') {
              return (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex items-center">
                    <div className="text-yellow-800">
                      <strong>Connection Issues Detected:</strong>
                      <ul className="mt-1 list-disc list-inside text-sm">
                        {report.issues.map((issue, index) => (
                          <li key={index}>{issue}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            }
            return null;
          })()}
        </CardContent>
      </Card>

      {/* The Debug Panel - Only show in development */}
      {process.env.NODE_ENV === 'development' && (
        <SocketDebugPanel socketData={socketData} userId={userId} />
      )}
    </div>
  );
}