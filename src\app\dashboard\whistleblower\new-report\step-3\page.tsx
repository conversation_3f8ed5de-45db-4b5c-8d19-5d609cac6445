"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { 
  Shield, 
  Home, 
  CheckCircle, 
  Calendar, 
  MapPin, 
  Users, 
  FileText, 
  AlertTriangle,
  Clock,
  Building,
  Mail,
  Phone
} from "lucide-react";

interface ReviewData {
  // Step 1 data
  title: string;
  category: string;
  location: string;
  description: string;
  isAnonymous: boolean;
  reportingPreferences: {
    emailUpdates: boolean;
    smsUpdates: boolean;
  };
  
  // Step 2 data
  incidentDate: string;
  incidentTime: string;
  specificLocation: string;
  departmentInvolved: string;
  witnessInfo: {
    hasWitnesses: boolean;
    witnessDetails: string;
  };
  evidenceInfo: {
    hasEvidence: boolean;
    evidenceDescription: string;
    files: File[];
  };
  previouslyReported: {
    hasBeenReported: boolean;
    reportedTo: string;
    reportedWhen: string;
    outcome: string;
  };
  impactAssessment: {
    financialImpact: string;
    reputationalImpact: string;
    operationalImpact: string;
    legalImpact: string;
  };
  urgencyLevel: string;
  additionalComments: string;
  followUpPreference: string;
}

export default function NewReportStep3Page() {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<ReviewData | null>(null);

  useEffect(() => {
    // Load data from both steps
    const step1Data = sessionStorage.getItem('reportStep1Data');
    const step2Data = sessionStorage.getItem('reportStep2Data');
    
    if (step1Data && step2Data) {
      const combinedData = {
        ...JSON.parse(step1Data),
        ...JSON.parse(step2Data)
      };
      setReportData(combinedData);
    } else {
      // Redirect back if no data
      router.push('/dashboard/whistleblower/new-report');
    }
  }, [router]);

  const handleFinalSubmit = async () => {
    if (!reportData) return;

    setLoading(true);
    try {
      const response = await fetch('/api/reports/final-submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...reportData,
          status: 'New',
          priority: reportData.urgencyLevel,
          submissionStep: 'final'
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success('Report submitted successfully!');
        // Clear session storage
        sessionStorage.removeItem('reportStep1Data');
        sessionStorage.removeItem('reportStep2Data');
        router.push('/dashboard/whistleblower/my-reports');
      } else {
        toast.error(result.error || 'Failed to submit report');
      }
    } catch (error) {
      console.error('Final submit error:', error);
      toast.error('Failed to submit report');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    router.push('/dashboard/whistleblower/new-report/step-2');
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return 'Not specified';
    return timeString;
  };

  if (!reportData) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower">
                  <Home className="w-4 h-4" />
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower/my-reports">
                  My Reports
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>New Report - Review</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              New Whistleblower Report - Review
            </h1>
            <p className="text-gray-600">
              Please review your report details before final submission
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full">
                  <CheckCircle className="w-5 h-5" />
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Step 1</span>
              </div>
              <div className="flex-1 h-1 bg-green-500 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full">
                  <CheckCircle className="w-5 h-5" />
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Step 2</span>
              </div>
              <div className="flex-1 h-1 bg-green-500 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-500 text-white rounded-full">
                  3
                </div>
                <span className="ml-2 text-sm font-medium text-blue-600">Review</span>
              </div>
            </div>
          </div>

          <Alert className="mb-6 border-blue-200 bg-blue-50">
            <Shield className="w-4 h-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Final Review:</strong> Please carefully review all information below. Once submitted, your report will be processed and you will receive a confirmation with a tracking number.
            </AlertDescription>
          </Alert>

          {/* Report Summary */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Report Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900">Report Title</h4>
                  <p className="text-gray-600">{reportData.title}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Category</h4>
                  <Badge variant="secondary">{reportData.category}</Badge>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Urgency Level</h4>
                  <Badge variant={reportData.urgencyLevel === 'Critical' ? 'destructive' : 
                                reportData.urgencyLevel === 'High' ? 'default' : 'secondary'}>
                    {reportData.urgencyLevel}
                  </Badge>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Anonymous Report</h4>
                  <Badge variant={reportData.isAnonymous ? 'outline' : 'secondary'}>
                    {reportData.isAnonymous ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
              <Separator />
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                <p className="text-gray-600 text-sm bg-gray-50 p-3 rounded">
                  {reportData.description}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Incident Details */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Incident Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">Date</h4>
                    <p className="text-gray-600">{formatDate(reportData.incidentDate)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">Time</h4>
                    <p className="text-gray-600">{formatTime(reportData.incidentTime)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">Location</h4>
                    <p className="text-gray-600">{reportData.specificLocation || reportData.location}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4 text-gray-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">Department</h4>
                    <p className="text-gray-600">{reportData.departmentInvolved || 'Not specified'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Witness Information */}
          {reportData.witnessInfo.hasWitnesses && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Witness Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 border border-green-200 p-3 rounded">
                  <p className="text-sm text-green-800">
                    <strong>Witnesses Present:</strong> Yes
                  </p>
                  {reportData.witnessInfo.witnessDetails && (
                    <p className="text-sm text-green-700 mt-2">
                      {reportData.witnessInfo.witnessDetails}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Evidence Information */}
          {reportData.evidenceInfo.hasEvidence && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Evidence & Documentation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 border border-blue-200 p-3 rounded">
                  <p className="text-sm text-blue-800">
                    <strong>Evidence Available:</strong> Yes
                  </p>
                  {reportData.evidenceInfo.evidenceDescription && (
                    <p className="text-sm text-blue-700 mt-2">
                      {reportData.evidenceInfo.evidenceDescription}
                    </p>
                  )}
                  {reportData.evidenceInfo.files.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-blue-800 font-medium">Files to be uploaded:</p>
                      <ul className="text-sm text-blue-700 mt-1">
                        {reportData.evidenceInfo.files.map((file, index) => (
                          <li key={index}>• {file.name}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Impact Assessment */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Impact Assessment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-orange-50 border border-orange-200 rounded">
                  <h4 className="font-medium text-orange-900">Financial</h4>
                  <p className="text-orange-700">{reportData.impactAssessment.financialImpact || 'Not assessed'}</p>
                </div>
                <div className="text-center p-3 bg-orange-50 border border-orange-200 rounded">
                  <h4 className="font-medium text-orange-900">Reputational</h4>
                  <p className="text-orange-700">{reportData.impactAssessment.reputationalImpact || 'Not assessed'}</p>
                </div>
                <div className="text-center p-3 bg-orange-50 border border-orange-200 rounded">
                  <h4 className="font-medium text-orange-900">Operational</h4>
                  <p className="text-orange-700">{reportData.impactAssessment.operationalImpact || 'Not assessed'}</p>
                </div>
                <div className="text-center p-3 bg-orange-50 border border-orange-200 rounded">
                  <h4 className="font-medium text-orange-900">Legal</h4>
                  <p className="text-orange-700">{reportData.impactAssessment.legalImpact || 'Not assessed'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Previous Reporting */}
          {reportData.previouslyReported.hasBeenReported && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Previous Reporting</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-purple-50 border border-purple-200 p-3 rounded">
                  <p className="text-sm text-purple-800">
                    <strong>Previously Reported:</strong> Yes
                  </p>
                  <div className="mt-2 space-y-1 text-sm text-purple-700">
                    <p><strong>Reported to:</strong> {reportData.previouslyReported.reportedTo}</p>
                    <p><strong>When:</strong> {formatDate(reportData.previouslyReported.reportedWhen)}</p>
                    {reportData.previouslyReported.outcome && (
                      <p><strong>Outcome:</strong> {reportData.previouslyReported.outcome}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Follow-up Preferences */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Follow-up Preferences</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">Preferred Method</h4>
                    <p className="text-gray-600">{reportData.followUpPreference}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">Notifications</h4>
                    <div className="flex gap-2">
                      {reportData.reportingPreferences.emailUpdates && (
                        <Badge variant="outline">Email</Badge>
                      )}
                      {reportData.reportingPreferences.smsUpdates && (
                        <Badge variant="outline">SMS</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Comments */}
          {reportData.additionalComments && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Additional Comments</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm bg-gray-50 p-3 rounded">
                  {reportData.additionalComments}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Final Confirmation */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Final Confirmation</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <AlertTriangle className="w-4 h-4" />
                <AlertDescription>
                  By submitting this report, you confirm that the information provided is accurate to the best of your knowledge. 
                  You will receive a confirmation email with a tracking number to monitor the progress of your report.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <Button
              variant="outline"
              onClick={goBack}
              disabled={loading}
            >
              Back to Step 2
            </Button>
            
            <Button
              onClick={handleFinalSubmit}
              disabled={loading}
              className="bg-[#1E4841] hover:bg-[#2A5D54]"
            >
              {loading ? 'Submitting...' : 'Submit Report'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}