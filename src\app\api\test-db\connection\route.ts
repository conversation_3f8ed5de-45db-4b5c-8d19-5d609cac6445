import { NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';


export const runtime = 'nodejs';

export async function GET() {
  try {
    // Authentication removed - DB connection test now available without auth
    
    // Test database connection only
    const mongoose = await connectDB();
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      details: {
        connection: 'Connected to MongoDB',
        host: mongoose.connection.host,
        name: mongoose.connection.name,
        readyState: mongoose.connection.readyState === 1 ? 'Connected' : 'Not connected'
      }
    });
  } catch (error) {
    console.error('❌ Database connection test failed:', error);
    
    // Handle authentication errors
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database connection test failed', 
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}