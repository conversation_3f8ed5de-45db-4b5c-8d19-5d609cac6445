'use client';

import { useParams } from 'next/navigation';
import { useState } from 'react';
import { loginSchema, ERROR_MESSAGES } from '@/lib';
import type { LoginFormData } from '@/lib';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { DEMO_CREDENTIALS } from '@/lib/mockData/demoCredentials';
import { useRouter } from 'next/navigation';

const roles = ['whistleblower', 'officer', 'admin'] as const;

export default function RoleLoginPage() {
  const params = useParams();
  const role = params.role as string;
  const [formData, setFormData] = useState<LoginFormData>({ email: '', password: '', remember: false });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  if (!roles.includes(role as typeof roles[number])) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <Card className="w-full max-w-sm sm:max-w-md">
          <CardContent className="pt-4 sm:pt-6">
            <Alert variant="destructive">
              <AlertDescription className="text-sm">Invalid role</AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getRoleVariant = () => {
    switch(role) {
      case 'whistleblower': return 'default';
      case 'officer': return 'outline';
      case 'admin': return 'destructive';
      default: return 'default';
    }
  };

  const getDemoCredentials = () => {
    return DEMO_CREDENTIALS[role as keyof typeof DEMO_CREDENTIALS] || { email: '', password: '' };
  };

  const handleDemoLogin = () => {
    const credentials = getDemoCredentials();
    setFormData(prev => ({ ...prev, ...credentials }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    const result = loginSchema.safeParse(formData);
    if (!result.success) {
      const fieldErrors = result.error.flatten().fieldErrors;
      setErrors(Object.fromEntries(
        Object.entries(fieldErrors).map(([key, value]) => [key, value?.[0] || ''])
      ));
      setIsLoading(false);
      return;
    }

    try {
      // Handle login logic here
      console.log('Login attempt:', result.data);
      // Redirect to dashboard
      router.push('/dashboard');
    } catch {
      setErrors({ general: ERROR_MESSAGES.generic });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-8">
      <Card className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
        <CardHeader className="px-4 sm:px-6 pt-4 sm:pt-6">
          <CardTitle className="text-center text-lg sm:text-xl lg:text-2xl">
            {role === 'officer' ? 'Investigator & Officer' : role.charAt(0).toUpperCase() + role.slice(1)} Login
          </CardTitle>
          <div className="text-center">
            <Button 
              type="button" 
              variant="ghost" 
              size="sm" 
              onClick={handleDemoLogin}
              className="text-blue-600 hover:text-blue-800 text-xs sm:text-sm"
            >
              Use Demo Credentials
            </Button>
          </div>
        </CardHeader>
        <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
          <form className="space-y-3 sm:space-y-4" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <Label htmlFor="email" className="text-xs sm:text-sm font-medium text-gray-700">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="h-10 sm:h-11 text-sm sm:text-base"
                required
              />
              {errors.email && (
                <p className="text-xs sm:text-sm text-destructive">{errors.email}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="password" className="text-xs sm:text-sm font-medium text-gray-700">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                className="h-10 sm:h-11 text-sm sm:text-base"
                required
              />
              {errors.password && (
                <p className="text-xs sm:text-sm text-destructive">{errors.password}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={formData.remember}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, remember: !!checked }))}
              />
              <Label htmlFor="remember" className="text-xs sm:text-sm font-normal">
                Remember me
              </Label>
            </div>
            {errors.general && (
              <Alert variant="destructive">
                <AlertDescription className="text-xs sm:text-sm">{errors.general}</AlertDescription>
              </Alert>
            )}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full h-10 sm:h-12 text-sm sm:text-base font-semibold"
              variant={getRoleVariant()}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
          </form>
          <div className="mt-3 sm:mt-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-xs sm:text-sm font-medium text-gray-700 mb-2">Demo Credentials:</p>
            <div className="text-xs text-gray-600 space-y-1">
              <div className="break-words"><strong>Email:</strong> {getDemoCredentials().email}</div>
              <div><strong>Password:</strong> {getDemoCredentials().password}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}