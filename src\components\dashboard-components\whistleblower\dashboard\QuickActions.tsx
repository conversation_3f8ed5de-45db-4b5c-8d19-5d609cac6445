"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, MessageSquare, Download, Settings, ShieldCheck } from "lucide-react";
import Link from "next/link";

export default function QuickActions() {
    return (
        <Card>
            <CardHeader className="py-0 flex items-center">
                <CardTitle className="p-0 py-1 text-sm sm:text-base text-[#242E2C] font-semibold">Quick Actions</CardTitle>
            </CardHeader>
            <Separator className="bg-[#F3F4F6]" />
            <CardContent className="space-y-3 sm:space-y-4">
                <Link href="/dashboard/whistleblower/reportissue">
                    <Button
                        aria-label="Submit New Report"
                        className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white text-sm sm:text-base font-normal py-4 sm:py-6 justify-start">
                        <Plus className="w-4 h-4 mr-2" />
                        Submit New Report
                    </Button>
                </Link>
                <Link href="/dashboard/whistleblower/secure-message">
                    <Button
                        aria-label="Access Secure Inbox"
                        variant="outline"
                        className="w-full hover text-sm sm:text-base font-normal py-4 sm:py-6 justify-start">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Access Secure Inbox
                    </Button>
                </Link>
                <Link href="/dashboard/whistleblower/my-reports">
                    <Button
                        aria-label="Download Report History"
                        variant="outline"
                        className="w-full text-sm sm:text-base font-normal py-4 sm:py-6 justify-start">
                        <Download className="w-4 h-4 mr-2" />
                        Download Report History
                    </Button>
                </Link>
                <Link href="/dashboard/whistleblower/profile-settings">
                    <Button
                        aria-label="Access Account Settings"
                        variant="outline"
                        className="w-full text-sm sm:text-base font-normal py-4 sm:py-6 justify-start">
                        <Settings className="w-4 h-4 mr-2" />
                        Account Settings
                    </Button>
                </Link>
                <Separator className="bg-[#F3F4F6] mt-2" />
                {/* Privacy Notice */}
                <div className="mt-3 sm:mt-5 p-3 sm:p-4 bg-[#ECF4E9] rounded-lg border border-[#ECF4E9]">
                    <div className="flex gap-3 sm:gap-4">
                        <ShieldCheck className="w-8 h-fit m-0 p-1 sm:p-1.5 bg-[#BBF49C] text-[#1E4841] rounded-full flex-shrink-0" />
                        <div className="flex flex-col">
                            <p className="font-medium text-sm sm:text-base text-[#1E4841] mb-1">Your Privacy Matters</p>
                            <p className="text-xs sm:text-sm font-normal text-[#1E4841] leading-relaxed">
                                All communications are encrypted and your identity is protected according to our whistleblower protection policy.
                            </p>
                            <div className="flex gap-2 mt-2 items-start sm:items-center">
                                <Checkbox name="privacy" id="privacy" aria-label="Enable enhanced security notifications" className="border-2 border-black mt-0.5 sm:mt-0" />
                                <Label htmlFor="privacy" id="privacy-label" className="text-xs sm:text-sm font-normal text-[#1E4841] leading-relaxed">
                                    Enable enhanced security notifications
                                </Label>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}