"use client";

import { useState, useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Bell, Home, CheckCheck } from "lucide-react";
import { notificationSystem } from "@/lib/utils/notificationSystem";
import { Notification } from "@/lib/types";
import { getNotificationIcon, getPriorityColor, formatTimestamp } from "@/lib/utils";

export default function NotificationsPage() {
    const [notifications, setNotifications] = useState<Notification[]>([]);

    useEffect(() => {
        setNotifications(notificationSystem.getNotifications());
        const unsubscribe = notificationSystem.subscribe(setNotifications);
        return unsubscribe;
    }, []);

    const handleMarkAsRead = (notificationId: string) => {
        notificationSystem.markAsRead(notificationId);
    };

    const handleMarkAllAsRead = () => {
        notifications.forEach(n => {
            if (n.status === 'unread') {
                notificationSystem.markAsRead(n._id!);
            }
        });
    };

    const unreadCount = notifications.filter(n => n.status === 'unread').length;

    return (
        <div className="w-full h-screen flex flex-col">
            <Header />
            <main className="flex-1 bg-gray-50">
                <header className="px-6 py-4 bg-white border-b">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard" className="flex items-center gap-1">
                                    <Home className="w-4 h-4" />
                                    Dashboard
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Notifications</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <div className="flex justify-between items-center mt-4">
                        <div>
                            <h1 className="text-2xl font-semibold text-[#242E2C]">Notifications</h1>
                            <p className="text-sm text-[#6B7280] mt-1">Stay updated with your case progress and system alerts</p>
                        </div>
                        {unreadCount > 0 && (
                            <Button onClick={handleMarkAllAsRead} className="bg-[#1E4841] text-white hover:bg-[#1E4841]/90">
                                <CheckCheck className="w-4 h-4 mr-2" />
                                Mark All Read
                            </Button>
                        )}
                    </div>
                </header>

                <div className="p-6">
                    {notifications.length > 0 ? (
                        <div className="bg-white rounded-lg shadow-sm">
                            {notifications.map((notification) => {
                                const Icon = getNotificationIcon(notification.type);
                                const priorityColor = getPriorityColor(notification.priority);
                                
                                return (
                                    <div
                                        key={notification._id}
                                        className={`p-4 border-b last:border-b-0 hover:bg-gray-50 transition-colors cursor-pointer ${
                                            notification.status === 'unread' ? 'bg-green-50/30 border-l-4 border-l-green-500' : ''
                                        }`}
                                        onClick={() => handleMarkAsRead(notification._id!)}
                                    >
                                        <div className="flex items-start gap-4">
                                            <div className={`p-2 rounded-full ${notification.status === 'unread' ? 'bg-green-100' : 'bg-gray-100'}`}>
                                                <Icon className={`w-5 h-5 ${priorityColor}`} />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <h3 className={`font-medium text-gray-900 ${notification.status === 'unread' ? 'font-semibold' : ''}`}>
                                                        {notification.title}
                                                    </h3>
                                                    <span className="text-sm text-gray-500">
                                                        {formatTimestamp(notification.createdAt?.toString() || '')}
                                                    </span>
                                                </div>
                                                <p className="text-gray-600 mt-1">{notification.message}</p>
                                                {notification.status === 'unread' && (
                                                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    ) : (
                        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                            <Bell className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications yet</h3>
                            <p className="text-gray-500">You&apos;ll see notifications about your reports and system updates here.</p>
                        </div>
                    )}
                </div>
            </main>
        </div>
    );
}