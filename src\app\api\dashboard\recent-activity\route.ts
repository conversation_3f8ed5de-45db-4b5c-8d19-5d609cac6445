import { NextRequest, NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { DataService } from '@/lib/db/dataService';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    
    // Get recent activity based on user role
    const userId = request.user!.role === 'whistleblower' ? request.user!.id : undefined;
    const companyId = request.user!.companyId;
    
    const activities = await DataService.getRecentActivity(userId, companyId, limit);
    
    return NextResponse.json({
      success: true,
      data: activities
    });
  } catch (error) {
    console.error('Recent activity API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});