import { config } from 'dotenv';
import { seedDatabase } from '../src/lib/db/seed';

config({ path: '.env.local' });

async function runSeed() {
  try {
    console.log('Starting database seeding...');
    const result = await seedDatabase();
    console.log('Seeding completed:', result);
    process.exit(0);
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  }
}

runSeed();