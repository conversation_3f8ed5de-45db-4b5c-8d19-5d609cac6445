import { Document, Types } from 'mongoose';

// Define interfaces for Mongoose documents that extend the base types
export interface UserDocument extends Document {
  passwordNeedsMigration: boolean;
  passwordHashAlgorithm: string;
  _id: Types.ObjectId;
  email: string;
  hashedPassword?: string;
  firstName?: string;
  lastName?: string;
  companyId?: Types.ObjectId;
  role: string;
  isActive: boolean;
  lastLogin?: Date;
  lastActive?: Date;
  sessionToken?: string;
  fullName: string; // Virtual property
  twoFactor?: {
    enabled: boolean;
    method: string;
    secret?: string;
    backupCodes?: string[];
    verificationCode?: string;
    verificationCodeExpires?: Date;
    attempts?: number;
  };
  failedLoginAttempts: number;
  accountLocked: boolean;
  accountLockedUntil?: Date;
  isAccountLocked(): boolean;
}

export interface ReportDocument extends Document {
  _id: Types.ObjectId;
  reportId: string;
  userId: Types.ObjectId | string;
  companyId?: Types.ObjectId | string;
  assignedInvestigator?: Types.ObjectId | string;
  status: string;
  updatedAt: Date;
  createdAt?: Date;
  title: string;
  description: string;
  category: string;
  priority: string;
  isAnonymous?: boolean;
  incidentDate?: Date;
  location?: string;
  progress?: number;
  estimatedCompletion?: Date;
  tags?: string[];
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    submissionMethod?: string;
  };
}

export interface NotificationDocument extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  reportId?: Types.ObjectId;
  status: string;
  type?: string;
  title?: string;
  message?: string;
  priority?: string;
  createdAt?: Date;
}

export interface ConversationDocument extends Document {
  _id: Types.ObjectId;
  reportId: Types.ObjectId;
  participants: Types.ObjectId[];
  status?: string;
  lastMessageAt?: Date;
  timestamp?: Date;
}

export interface MessageDocument extends Document {
  _id: Types.ObjectId;
  conversationId: Types.ObjectId;
  senderId: Types.ObjectId;
  content: string;
  messageType: string;
  isEncrypted: boolean;
  timestamp?: Date;
  createdAt?: Date;
}

export interface PricingPlanDocument extends Document {
  _id: Types.ObjectId;
  name: string;
  price: number;
  features: string[];
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CompanyDocument extends Document {
  _id: Types.ObjectId;
  name: string;
  industry?: string;
  size?: string;
  logo?: string;
  website?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  contactEmail?: string;
  contactPhone?: string;
  subscriptionStatus?: string;
  subscriptionPlan?: Types.ObjectId;
  subscriptionStartDate?: Date;
  subscriptionEndDate?: Date;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface BlogDocument extends Document {
  _id: Types.ObjectId;
  title: string;
  slug: string;
  content?: string;
  date: string;
  description: string;
  category: string;
  tags: string[];
  image: string;
  author?: string;
  featured: boolean;
  createdAt: Date;
  updatedAt: Date;
}