// Navigation-related mock data
import { LayoutDashboard, MenuSquare, MessageSquareMore, Settings, HelpCircleIcon, Users, Bell, FileText, AlertTriangle, BarChart3, Activity } from "lucide-react";

// Navigation Items
export const ABOUT_ITEMS = [
    { href: "/about/company", title: "Company", description: "Learn more about our company history and mission" },
    { href: "/about/team", title: "Team", description: "Meet our leadership and development teams" },
    { href: "/about/careers", title: "Careers", description: "View current job openings and opportunities" }
];

export const BLOG_ITEMS = [
    { href: "/blog/tech", title: "Technology", description: "Latest updates in tech and development" },
    { href: "/blog/industry", title: "Industry News", description: "Insights and trends in our industry" },
    { href: "/blog/company", title: "Company Updates", description: "News and announcements from our team" }
];

export const PRODUCT_ITEMS = [
    {
        href: "/products/whistleblower",
        title: "Whistleblower Portal",
        description: "A secure platform for employees and stakeholders to submit reports anonymously and safely.",
        cta: "Explore Report Hub"
    },
    {
        href: "/products/investigator",
        title: "Investigator Portal",
        description: "A purpose-built workspace to manage investigations, review evidence, and maintain compliance.",
        cta: "Explore Case Center"
    },
    {
        href: "/products/admin",
        title: "Admin Portal",
        description: "Monitor all whistleblowing activities, assign cases, configure workflows, and generate insights.",
        cta: "Explore Control Suite"
    }
];

export const LOGIN_ITEMS = [
    { href: "/login/whistleblower", title: "Whistleblower" },
    { href: "/login/officer", title: "Internal Investigator & Compliance Officer" },
    { href: "/login/admin", title: "System Admin" }
];

export const SIGNUP_ITEMS = [
    { href: "/signup/whistleblower", title: "Whistleblower" },
    { href: "/signup/admin", title: "System Admin" }
];

export const MOBILE_NAV_ITEMS = [
    { href: "/", title: "Home" },
    { href: "/products", title: "Products" },
    { href: "/pricing", title: "Pricing" },
    { href: "/contact", title: "Contact" }
];

// Footer Links
export const SOCIAL_LINKS = [
    { icon: 'facebook', name: 'Facebook', href: "" },
    { icon: 'instagram', name: 'Instagram', href: "" },
    { icon: 'twitter', name: 'Twitter', href: "" },
    { icon: 'linkedin', name: 'LinkedIn', href: "" },
    { icon: 'youtube', name: 'YouTube', href: "" }
];

export const WHISTLEBLOWING_LINKS = [
    { href: 'facebook', text: 'Compliance network' },
    { href: 'instagram', text: 'Whistleblowing System Guide' },
    { href: 'twitter', text: 'Whistleblowing Directive Summary' }
];

export const SYSTEM_LINKS = [
    { href: '/products', text: 'Product' },
    { href: '/pricing', text: 'Pricing' },
    { href: '/blog', text: 'Blog' },
    { href: '/about', text: 'About Us' },
    { href: '/contact', text: 'Contact' }
];

export const FOOTER_LINKS = [
    { href: '/privacy-policy', text: 'Privacy Policy' },
    { href: '/terms-of-service', text: 'Terms of Service' },
    { href: '/cookies-settings', text: 'Cookies Settings' }
];

// Header Navigation Data
export const PROFILE_ITEMS = [
    { href: "/change-profile", title: "Change Profile" },
    { href: "/settings", title: "Settings" },
    { href: "/logout", title: "Logout" }
];

// Navigation items for mobile menu (from Sidebar)
export const NAVIGATION_ITEMS = [
    {
        title: "Dashboard",
        url: "/dashboard/whistleblower",
        icon: LayoutDashboard,
    },
    {
        title: "My Reports",
        url: "/dashboard/whistleblower/my-reports",
        icon: MenuSquare,
    },
    {
        title: "New Report",
        url: "/dashboard/whistleblower/new-report",
        icon: FileText,
    },
    {
        title: "Secure Messages",
        url: "/dashboard/whistleblower/secure-message",
        icon: MessageSquareMore,
    },
    {
        title: "Profile & Settings",
        url: "/dashboard/whistleblower/profile-settings",
        icon: Settings,
    },
    {
        title: "Help / FAQ",
        url: "/dashboard/whistleblower/help",
        icon: HelpCircleIcon,
    },
];

// Admin Navigation Items with hierarchical structure
export const ADMIN_NAVIGATION_ITEMS = [
    {
        title: "Main",
        items: [
            {
                title: "Dashboard",
                url: "/dashboard/admin",
                icon: LayoutDashboard,
            },
            {
                title: "Case Management",
                url: "/dashboard/admin/case-management",
                icon: MenuSquare,
            },
            {
                title: "User Management",
                url: "/dashboard/admin/user-management",
                icon: Users,
            },
            {
                title: "Notifications",
                url: "/dashboard/admin/notifications",
                icon: Bell,
            },
            {
                title: "Communications",
                url: "/dashboard/admin/secure-message",
                icon: MessageSquareMore,
            },
        ]
    },
    {
        title: "Management",
        items: [
            {
                title: "Escalations & SLAs",
                url: "/dashboard/admin/escalations",
                icon: AlertTriangle,
            },
            {
                title: "Audit Logs",
                url: "/dashboard/admin/audit-logs",
                icon: FileText,
            },
            {
                title: "Reports & Analytics",
                url: "/dashboard/admin/reports-analytics",
                icon: BarChart3,
            },
        ]
    },
    {
        title: "System",
        items: [
            {
                title: "Settings",
                url: "/dashboard/admin/profile-settings",
                icon: Settings,
            },
            {
                title: "Help & Support",
                url: "/dashboard/admin/help",
                icon: HelpCircleIcon,
            },
            {
                title: "Real-time Demo",
                url: "/dashboard/admin/real-time-demo",
                icon: Activity,
            },
        ]
    }
];
