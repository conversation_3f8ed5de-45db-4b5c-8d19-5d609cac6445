"use client";

import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import DashboardSidebar from "@/components/dashboard-components/Sidebar";
import ProtectedRoute from "@/components/auth-components/ProtectedRoute";
import { RealTimeProvider } from "@/providers/RealTimeProvider";

export default function Layout({ children }: { children: React.ReactNode }) {

  return (
    <ProtectedRoute requiredRole="whistleblower" redirectTo="/login/whistleblower">
      <RealTimeProvider>
        <SidebarProvider className="w-full bg-[#F9FAFB]">
          <DashboardSidebar />
          <SidebarInset className="overflow-hidden w-full">
            <div className="w-full h-full" role="main" aria-label="Dashboard content area">
              {children}
            </div>
          </SidebarInset>
        </SidebarProvider>
      </RealTimeProvider>
    </ProtectedRoute>
  )
}