"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Eye, Edit, AlertTriangle } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from "@/components/ui/data-table";
import { adminRecentCases } from "@/lib/mockData";
import { ColumnDef } from "@tanstack/react-table";

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'new':
            return 'bg-green-100 text-green-800';
        case 'in progress':
            return 'bg-yellow-100 text-yellow-800';
        case 'closed':
            return 'bg-gray-100 text-gray-800';
        case 'escalated':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
        case 'high':
            return 'bg-red-100 text-red-800';
        case 'medium':
            return 'bg-yellow-100 text-yellow-800';
        case 'low':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

type CaseData = {
    id: string;
    subject: string;
    department: string;
    status: string;
    priority: string;
    date: string;
};

const columns: ColumnDef<CaseData>[] = [
    {
        accessorKey: "id",
        header: "Case ID",
        cell: ({ row }) => {
            const case_item = row.original;
            return (
                <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">#{case_item.id}</span>
                    {case_item.priority === 'High' && (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                </div>
            );
        },
    },
    {
        accessorKey: "subject",
        header: "Subject",
        cell: ({ row }) => (
            <div className="max-w-[200px] truncate font-medium">
                {row.getValue("subject")}
            </div>
        ),
    },
    {
        accessorKey: "department",
        header: "Department",
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            return (
                <Badge variant="secondary" className={`text-xs ${getStatusColor(status)}`}>
                    {status}
                </Badge>
            );
        },
    },
    {
        accessorKey: "priority",
        header: "Priority",
        cell: ({ row }) => {
            const priority = row.getValue("priority") as string;
            return (
                <Badge variant="secondary" className={`text-xs ${getPriorityColor(priority)}`}>
                    {priority}
                </Badge>
            );
        },
    },
    {
        accessorKey: "date",
        header: "Date",
    },
    {
        id: "actions",
        cell: ({ }) => {
            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Case
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        },
    },
];

export default function AdminRecentCases() {
    return (
        <Card className="bg-white border-0 shadow-sm h-full">
            <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-base sm:text-lg font-semibold text-gray-900">
                        Recent Cases
                    </CardTitle>
                    <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                        View All
                    </Button>
                </div>
            </CardHeader>
            <CardContent className="pt-0">
                <DataTable 
                    columns={columns} 
                    data={adminRecentCases.slice(0, 8)} 
                    searchKey="subject"
                    searchPlaceholder="Search cases..."
                />
            </CardContent>
        </Card>
    );
}