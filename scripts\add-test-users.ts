import { config } from 'dotenv';
import mongoose from 'mongoose';
import { createHash } from 'crypto';
import { User, Company } from '../src/lib/db/models';

config({ path: '.env.local' });

// Simple hash function for passwords
function hashPassword(password: string): string {
  return createHash('sha256').update(password).digest('hex');
}

async function addTestUsers() {
  try {
    console.log('Connecting to MongoDB...');
    const MONGODB_URI = process.env.MONGODB_URI!;
    
    if (!MONGODB_URI) {
      throw new Error('Please define the MONGODB_URI environment variable');
    }
    
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get first company or create one if none exists
    let company = await Company.findOne();
    if (!company) {
      company = await Company.create({
        name: 'Test Company',
        industry: 'Technology',
        size: 'Medium',
        subscriptionStatus: 'Active'
      });
      console.log('Created test company');
    }

    // Test users with known credentials
    const testUsers = [
      {
        email: '<EMAIL>',
        hashedPassword: hashPassword('admin123'),
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        companyId: company._id,
        emailVerified: true,
        isActive: true,
        twoFactor: { enabled: false }
      },
      {
        email: '<EMAIL>',
        hashedPassword: hashPassword('password123'),
        firstName: 'John',
        lastName: 'Investigator',
        role: 'investigator',
        companyId: company._id,
        emailVerified: true,
        isActive: true,
        twoFactor: { enabled: false }
      },
      {
        email: '<EMAIL>',
        hashedPassword: hashPassword('password123'),
        firstName: 'Jane',
        lastName: 'Whistleblower',
        role: 'whistleblower',
        companyId: company._id,
        emailVerified: true,
        isActive: true,
        twoFactor: { enabled: false }
      },
      {
        email: '<EMAIL>',
        hashedPassword: hashPassword('password123'),
        firstName: 'Two-Factor',
        lastName: 'User',
        role: 'admin',
        companyId: company._id,
        emailVerified: true,
        isActive: true,
        twoFactor: { 
          enabled: true,
          method: 'email',
          verificationCode: '123456',
          verificationCodeExpires: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
        }
      },
      {
        email: '<EMAIL>',
        hashedPassword: hashPassword('password123'),
        firstName: 'Pending',
        lastName: 'User',
        role: 'admin',
        companyId: company._id,
        emailVerified: true,
        isActive: false,
        twoFactor: { enabled: false }
      }
    ];

    // Insert users one by one with upsert
    const results = [];
    for (const user of testUsers) {
      try {
        const result = await User.findOneAndUpdate(
          { email: user.email },
          user,
          { upsert: true, new: true }
        );
        results.push(result);
        console.log(`Added/updated user: ${user.email}`);
      } catch (error) {
        console.error(`Failed to add user ${user.email}:`, error);
      }
    }

    console.log(`Successfully added/updated ${results.length} test users`);
    process.exit(0);
  } catch (error) {
    console.error('Error adding test users:', error);
    process.exit(1);
  }
}

addTestUsers();