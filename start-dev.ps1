#!/usr/bin/env pwsh

Write-Host "Starting 7IRIS Whistleblower Platform Development Environment" -ForegroundColor Green
Write-Host ""

Write-Host "Starting WebSocket Server on port 3001..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "node src/lib/websocket/devServer.js" -WindowStyle Normal

Write-Host "Waiting for WebSocket server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host "Starting Main Application on port 3000..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "node server.js" -WindowStyle Normal

Write-Host ""
Write-Host "Development environment started!" -ForegroundColor Green
Write-Host ""
Write-Host "Main Application: http://localhost:3000" -ForegroundColor Cyan
Write-Host "WebSocket Server: ws://localhost:3001/ws" -ForegroundColor Cyan
Write-Host ""
Write-Host "Real-time Demo Pages:" -ForegroundColor Magenta
Write-Host "- Whistleblower: http://localhost:3000/dashboard/whistleblower/real-time-demo" -ForegroundColor Cyan
Write-Host "- Admin: http://localhost:3000/dashboard/admin/real-time-demo" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")