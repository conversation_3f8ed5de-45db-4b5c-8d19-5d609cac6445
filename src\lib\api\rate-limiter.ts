import { NextRequest, NextResponse } from 'next/server';

// In-memory store for rate limiting
const store = new Map();

// Create a rate limiter for authentication endpoints
export const authRateLimiter = {
  // Check if the request is allowed
  check: async (request: NextRequest): Promise<{ allowed: boolean; limit: number; remaining: number }> => {
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0].trim() : request.headers.get('x-real-ip') || '127.0.0.1';
    const key = `auth:${ip}`;
    
    // Get current timestamp
    const now = Date.now();
    
    // Get existing data for this IP
    const data = store.get(key) || { count: 0, resetTime: now + 15 * 60 * 1000 }; // 15 minutes window
    
    // Reset if outside window
    if (now > data.resetTime) {
      data.count = 0;
      data.resetTime = now + 15 * 60 * 1000;
    }
    
    // Check if limit exceeded
    const limit = 10; // 10 requests per 15 minutes
    const allowed = data.count < limit;
    
    // Increment counter if allowed
    if (allowed) {
      data.count++;
      store.set(key, data);
    }
    
    return {
      allowed,
      limit,
      remaining: Math.max(0, limit - data.count)
    };
  },
  
  // Create a middleware response if rate limited
  buildResponse: (result: { allowed: boolean; limit: number; remaining: number }): NextResponse | null => {
    if (!result.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many login attempts. Please try again later.'
        },
        {
          status: 429,
          headers: {
            'Retry-After': '900', // 15 minutes in seconds
            'X-RateLimit-Limit': result.limit.toString(),
            'X-RateLimit-Remaining': result.remaining.toString()
          }
        }
      );
    }
    
    return null;
  }
};