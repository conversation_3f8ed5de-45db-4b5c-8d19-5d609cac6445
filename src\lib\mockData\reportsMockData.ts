import { ReportData, ActivityItem } from "@/lib/types";

export const mockReportsData: ReportData[] = [
  {
    id: "WB-2025-0012",
    title: "Potential accounting irregularities in Q1 reports",
    status: "Awaiting Response",
    statusColor: "bg-yellow-100 text-yellow-800",
    dateSubmitted: "May 10, 2025",
    lastUpdated: "May 14, 2025",
    priority: "High",
    category: "Financial",
    progress: "65",
    progressPercentage: 65
  },
  {
    id: "WB-2025-0011",
    title: "Workplace safety concerns in manufacturing plant B",
    status: "Under Review",
    statusColor: "bg-orange-100 text-orange-800",
    dateSubmitted: "May 3, 2025",
    lastUpdated: "May 12, 2025",
    priority: "Critical",
    category: "Workplace Safety",
    progress: "45",
    progressPercentage: 45
  },
  {
    id: "WB-2025-0010",
    title: "Inappropriate conduct by senior manager",
    status: "Under Review",
    statusColor: "bg-orange-100 text-orange-800",
    dateSubmitted: "Apr 28, 2025",
    lastUpdated: "May 5, 2025",
    priority: "High",
    category: "Harassment",
    progress: "35",
    progressPercentage: 35
  },
  {
    id: "WB-2025-0009",
    title: "Potential data privacy breach in customer databases",
    status: "Under Review",
    statusColor: "bg-orange-100 text-orange-800",
    dateSubmitted: "Apr 15, 2025",
    lastUpdated: "May 8, 2025",
    priority: "Critical",
    category: "Data Privacy",
    progress: "50",
    progressPercentage: 50
  },
  {
    id: "WB-2025-0008",
    title: "Conflict of interest in vendor selection process",
    status: "Resolved",
    statusColor: "bg-green-100 text-green-800",
    dateSubmitted: "Apr 2, 2025",
    lastUpdated: "Apr 30, 2025",
    priority: "Medium",
    category: "Ethics Violation",
    progress: "100",
    progressPercentage: 100
  },
  {
    id: "WB-2025-0007",
    title: "Suspected misuse of company resources by department head",
    status: "Resolved",
    statusColor: "bg-green-100 text-green-800",
    dateSubmitted: "Mar 18, 2025",
    lastUpdated: "Apr 22, 2025",
    priority: "Medium",
    category: "Ethics Violation",
    progress: "100",
    progressPercentage: 100
  },
  {
    id: "WB-2025-0006",
    title: "Environmental compliance concerns at production facility",
    status: "Resolved",
    statusColor: "bg-green-100 text-green-800",
    dateSubmitted: "Mar 5, 2025",
    lastUpdated: "Apr 10, 2025",
    priority: "Medium",
    category: "Workplace Safety",
    progress: "100",
    progressPercentage: 100
  },
  {
    id: "WB-2025-0005",
    title: "Fraudulent expense claims by department head",
    status: "Resolved",
    statusColor: "bg-green-100 text-green-800",
    dateSubmitted: "Feb 28, 2025",
    lastUpdated: "Apr 10, 2025",
    priority: "High",
    category: "Financial",
    progress: "100",
    progressPercentage: 100
  },
  {
    id: "WB-2025-0004",
    title: "Misuse of company resources for personal gain",
    status: "New",
    statusColor: "bg-blue-100 text-blue-800",
    dateSubmitted: "May 15, 2025",
    lastUpdated: "May 15, 2025",
    priority: "Medium",
    category: "Ethics Violation",
    progress: "0",
    progressPercentage: 0
  },
  {
    id: "WB-2025-0003",
    title: "Violation of customer data protection policies",
    status: "Awaiting Response",
    statusColor: "bg-yellow-100 text-yellow-800",
    dateSubmitted: "May 8, 2025",
    lastUpdated: "May 13, 2025",
    priority: "Critical",
    category: "Data Privacy",
    progress: "75",
    progressPercentage: 75
  },
  {
    id: "WB-2025-0002",
    title: "Unsafe working conditions in warehouse facility",
    status: "New",
    statusColor: "bg-blue-100 text-blue-800",
    dateSubmitted: "Apr 22, 2025",
    lastUpdated: "May 6, 2025",
    priority: "High",
    category: "Workplace Safety",
    progress: "10",
    progressPercentage: 10
  },
  {
    id: "WB-2025-0001",
    title: "Bribery and corruption in procurement process",
    status: "Resolved",
    statusColor: "bg-green-100 text-green-800",
    dateSubmitted: "Jan 15, 2025",
    lastUpdated: "Mar 30, 2025",
    priority: "Critical",
    category: "Ethics Violation",
    progress: "100",
    progressPercentage: 100
  }
];

export const mockActivityItems: ActivityItem[] = [
  {
    id: "activity_001",
    type: "message",
    title: "New Message from Investigator",
    description: "We need additional information regarding the accounting irregularities you reported.",
    time: "2 hours ago",
    reportId: "WB-2025-0012",
    icon: "message",
    iconColor: "#2563EB",
    actionButton: {
      text: "Reply",
      action: "reply"
    }
  },
  {
    id: "activity_002",
    type: "status_update",
    title: "Report Status Updated",
    time: "1 day ago",
    reportId: "WB-2025-0011",
    icon: "status",
    iconColor: "#EA580C",
    metadata: {
      previousStatus: "New",
      currentStatus: "Under Review"
    },
    actionButton: {
      text: "View Report",
      action: "view"
    }
  },
  {
    id: "activity_003",
    type: "resolution",
    title: "Report Resolved",
    time: "3 days ago",
    reportId: "WB-2025-0008",
    icon: "check",
    iconColor: "#16A34A",
    metadata: {
      finalStatus: "Resolved"
    },
    actionButton: {
      text: "View Resolution",
      action: "resolution"
    }
  }
];