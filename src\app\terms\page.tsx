import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";

export default function Terms() {
  return (
    <div>
      <Header />
      <main id="main-content" className="pt-20 min-h-screen">
        <div className="container mx-auto px-4 py-16">
          <h1 className="h1-bold text-center mb-8">Terms of Service</h1>
          <div className="max-w-4xl mx-auto prose">
            <p className="body-16-regular mb-6">
              These Terms of Service govern your use of our whistleblowing platform and services.
            </p>
            <h2 className="h3-semibold mb-4">Acceptance of Terms</h2>
            <p className="body-14-regular mb-4">
              By accessing and using our service, you accept and agree to be bound by the terms and provision of this agreement.
            </p>
            <h2 className="h3-semibold mb-4">Use License</h2>
            <p className="body-14-regular mb-4">
              Permission is granted to temporarily use our service for personal, non-commercial transitory viewing only.
            </p>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}