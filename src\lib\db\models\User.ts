import { Schema } from 'mongoose';
import { createModel } from '../utils';

const UserPreferencesSchema = new Schema({
  language: { type: String, default: 'en' },
  notifications: {
    email: { type: Boolean, default: true },
    push: { type: <PERSON>olean, default: true },
    sms: { type: Boolean, default: false }
  },
  theme: { type: String, enum: ['light', 'dark', 'system'], default: 'light' }
});

const TwoFactorSchema = new Schema({
  enabled: { type: Boolean, default: false, required: true },
  method: { type: String, enum: ['email', 'app'], default: 'email', required: true },
  secret: { type: String },
  backupCodes: [{ type: String }],
  verificationCode: { type: String },
  verificationCodeExpires: { type: Date },
  attempts: { type: Number, default: 0 }
});

const UserSchema = new Schema({
  email: { type: String, required: true, unique: true },
  hashedPassword: { type: String },
  firstName: { type: String },
  lastName: { type: String },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company' },
  role: { 
    type: String, 
    enum: ['whistleblower', 'investigator', 'admin'], 
    required: true 
  },
  isActive: { type: Boolean, default: true },
  lastLogin: { type: Date },
  preferences: { type: UserPreferencesSchema, default: {} },
  twoFactor: { type: TwoFactorSchema, default: {} },
  passwordResetToken: { type: String },
  passwordResetExpires: { type: Date },
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: { type: String },
  failedLoginAttempts: { type: Number, default: 0 },
  accountLocked: { type: Boolean, default: false },
  accountLockedUntil: { type: Date },
  unlockToken: { type: String },
  unlockTokenExpires: { type: Date },
  passwordNeedsMigration: { type: Boolean, default: false },
  passwordHashAlgorithm: { type: String, enum: ['sha256', 'bcrypt'], default: 'bcrypt' },
  phoneNumber: { type: String },
  profileImage: { type: String }
}, {
  timestamps: true
});

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`;
  }
  return this.email.split('@')[0];
});

// Method to check if account is locked
UserSchema.methods.isAccountLocked = function() {
  if (!this.accountLocked) return false;
  if (this.accountLockedUntil && new Date() > this.accountLockedUntil) {
    this.accountLocked = false;
    this.accountLockedUntil = undefined;
    this.failedLoginAttempts = 0;
    return false;
  }
  return true;
};

// Use the utility function to create the model safely
const User = createModel('User', UserSchema);

export default User;