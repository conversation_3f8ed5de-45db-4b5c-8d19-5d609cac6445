import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";

export default function Privacy() {
  return (
    <div>
      <Header />
      <main id="main-content" className="pt-20 min-h-screen">
        <div className="container mx-auto px-4 py-16">
          <h1 className="h1-bold text-center mb-8">Privacy Policy</h1>
          <div className="max-w-4xl mx-auto prose">
            <p className="body-16-regular mb-6">
              Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your information.
            </p>
            <h2 className="h3-semibold mb-4">Information We Collect</h2>
            <p className="body-14-regular mb-4">
              We collect information you provide directly to us, such as when you create an account, submit a report, or contact us.
            </p>
            <h2 className="h3-semibold mb-4">How We Use Your Information</h2>
            <p className="body-14-regular mb-4">
              We use the information we collect to provide, maintain, and improve our services, and to communicate with you.
            </p>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}