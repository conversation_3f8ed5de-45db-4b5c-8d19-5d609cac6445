import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON> } from "next/font/google";

const inter = Inter({
    weight: ["400", "600"],
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-inter'
});

const sen = Sen({
    weight: ["400", "600"],
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-sen'
});

const interFontClass = `font-[${inter.variable}]`;
const senFontClass = `font-[${sen.variable}]`;

export default function Contact() {
    return (
        <div className={`flex flex-col justify-between items-center gap-3 sm:gap-4 md:gap-5 lg:gap-6 text-center w-50 md:w-120 lg:w-150 mx-auto my-25`}>
            <h2 className={`text-[#1E4841] text-2xl/8 md:text-3xl/10 lg:text-4xl/12 font-bold  ${senFontClass}`}>Have questions or need a demo? We&apos;re here to help!</h2>
            <p className={`w-full sm:w-4/5 md:w-4/5 lg:w-3/5 text-[#6D6E76] text-sm/6 sm:text-base/6 md:text-base/7 font-normal ${interFontClass}`}>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</p>
            <Button
                variant="default"
                className="w-fit px-4 py-2 sm:px-5 sm:py-2.5 md:px-8 md:py-4 lg:px-10 lg:py-6 text-xs sm:text-sm md:text-base lg:text-lg text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
            >
                Contact Us
            </Button>
        </div>
    );

}