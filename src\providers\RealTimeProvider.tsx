"use client";

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useRealTimeUpdates } from '@/hooks/useRealTimeUpdates';
import { getRealTimeNotificationSystem } from '@/lib/utils/realTimeNotificationSystem';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface RealTimeContextType {
  // Connection status
  isConnected: boolean;
  connectionHealth: {
    connectedUsers: number;
  };

  // Stats and data
  stats: unknown;
  notifications: unknown[];
  
  // Messaging
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  sendMessage: (conversationId: string, content: string, messageType?: 'text' | 'file' | 'system') => void;
  markMessageAsRead: (messageId: string) => void;
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;
  
  // Notifications
  unreadNotificationCount: number;
  requestStats: () => void;
  requestNotifications: () => void;
  
  // Actions
  requestNotificationPermission: () => Promise<NotificationPermission>;
}

const RealTimeContext = createContext<RealTimeContextType | null>(null);

interface RealTimeProviderProps {
  children: ReactNode;
}

export function RealTimeProvider({ children }: RealTimeProviderProps) {
  const { isAuthenticated } = useAuth();
  
  // Initialize real-time updates only when authenticated
  const realTimeUpdates = useRealTimeUpdates();

  // Handle real-time notifications
  useEffect(() => {
    if (isAuthenticated && realTimeUpdates.isConnected) {
      // Listen for new notifications
      const handleNewNotification = (event: CustomEvent) => {
        const notification = event.detail.notification;
        
        if (notification.priority === 'urgent') {
          toast.error(notification.title, {
            description: notification.message,
            duration: 10000,
            action: {
              label: 'View',
              onClick: () => {
                window.location.href = notification.actionUrl || '/dashboard/notifications';
              }
            }
          });
        } else if (notification.priority === 'high') {
          toast.warning(notification.title, {
            description: notification.message,
            duration: 7000
          });
        } else {
          toast.info(notification.title, {
            description: notification.message,
            duration: 5000
          });
        }
      };

      // Listen for custom events from the real-time hook
      window.addEventListener('new-real-time-notification', handleNewNotification as EventListener);

      return () => {
        window.removeEventListener('new-real-time-notification', handleNewNotification as EventListener);
      };
    }
  }, [isAuthenticated, realTimeUpdates.isConnected]);

  // Handle connection status changes
  useEffect(() => {
    if (realTimeUpdates.isConnected) {
      toast.success('Connected', {
        description: 'Real-time updates are now active',
        duration: 3000
      });
    }
  }, [realTimeUpdates.isConnected]);

  // Handle beforeunload to cleanup
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isAuthenticated && realTimeUpdates.socket) {
        realTimeUpdates.socket.disconnect();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isAuthenticated, realTimeUpdates.socket]);

  const contextValue: RealTimeContextType = {
    // Connection status
    isConnected: realTimeUpdates.isConnected,
    connectionHealth: {
      connectedUsers: realTimeUpdates.connectedUsers
    },

    // Stats and data
    stats: realTimeUpdates.stats,
    notifications: realTimeUpdates.notifications,

    // Messaging
    joinConversation: realTimeUpdates.joinConversation,
    leaveConversation: realTimeUpdates.leaveConversation,
    sendMessage: realTimeUpdates.sendMessage,
    markMessageAsRead: realTimeUpdates.markMessageAsRead,
    startTyping: realTimeUpdates.startTyping,
    stopTyping: realTimeUpdates.stopTyping,

    // Notifications
    unreadNotificationCount: realTimeUpdates.notifications.filter(n => n.status === 'unread').length,
    requestStats: realTimeUpdates.requestStats,
    requestNotifications: realTimeUpdates.requestNotifications,

    // Actions
    requestNotificationPermission: getRealTimeNotificationSystem().requestNotificationPermission
  };

  return (
    <RealTimeContext.Provider value={contextValue}>
      {children}
    </RealTimeContext.Provider>
  );
}

export function useRealTime(): RealTimeContextType {
  const context = useContext(RealTimeContext);
  if (!context) {
    throw new Error('useRealTime must be used within a RealTimeProvider');
  }
  return context;
}

// Hook for components that need to show connection status
export function useConnectionStatus() {
  const { isConnected, connectionHealth } = useRealTime();
  
  return {
    isConnected,
    connectionHealth,
    status: isConnected ? 'connected' : 'disconnected'
  };
}

// Hook for components that need notification status
export function useNotificationStatus() {
  const { unreadNotificationCount, requestNotificationPermission } = useRealTime();
  
  return {
    unreadCount: unreadNotificationCount,
    hasUnread: unreadNotificationCount > 0,
    requestPermission: requestNotificationPermission
  };
}