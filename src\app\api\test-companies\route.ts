import { NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { Company } from '@/lib/db/models';

export const runtime = 'nodejs';

export async function GET() {
  try {
    await connectDB();
    
    const companies = await Company.find({}).select('name email domain isActive');
    
    return NextResponse.json({
      success: true,
      count: companies.length,
      data: companies
    });
  } catch (error) {
    console.error('Test companies API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}