@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* Custom Colors */
  --color-light-blue: #E8F5FF;
  --color-dark-green: #1E4841;
  --color-light-green: #BBF49C;
  --color-pale-green: #ECF4E9;
  --color-red: #FF434E;
  --color-light-red: #FFACB1;
  --color-gray: #A4A4A4;
  --color-dark-gray: #111827;
  --color-medium-gray: #374151;
  --color-slate-gray: #4B5563;
  --color-light-gray: #6B7280;
  --color-off-white: #FBFBFC;

  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }
  
  html {
    overflow-x: hidden;
  }
  
  /* Override Radix UI body modifications */
  body[data-scroll-locked] {
    padding-right: 0 !important;
    margin-right: 0 !important;
    overflow: visible !important;
  }
  
  body[style*="padding-right"],
  body[style*="margin-right"] {
    padding-right: 0 !important;
    margin-right: 0 !important;
  }
}

@layer utilities {
  /* Headings */
  .h1-semibold { @apply text-[32px] font-semibold leading-tight; }
  .h1-bold { @apply text-[32px] font-bold leading-tight; }
  .h2-semibold { @apply text-[28px] font-semibold leading-tight; }
  .h2-bold { @apply text-[28px] font-bold leading-tight; }
  .h3-semibold { @apply text-[26px] font-semibold leading-tight; }
  .h3-bold { @apply text-[26px] font-bold leading-tight; }
  .h4-semibold { @apply text-[24px] font-semibold leading-tight; }
  .h4-bold { @apply text-[24px] font-bold leading-tight; }
  .h5-bold { @apply text-[22px] font-bold leading-tight; }
  .h5-extrabold { @apply text-[22px] font-extrabold leading-tight; }
  .h6-bold { @apply text-[20px] font-bold leading-tight; }
  .h6-extrabold { @apply text-[20px] font-extrabold leading-tight; }

  /* Titles */
  .title-18-semibold { @apply text-[18px] font-semibold; }
  .title-18-bold { @apply text-[18px] font-bold; }
  .title-16-regular { @apply text-[16px] font-normal; }
  .title-16-semibold { @apply text-[16px] font-semibold; }
  .title-16-bold { @apply text-[16px] font-bold; }
  .title-14-regular { @apply text-[14px] font-normal; }
  .title-14-semibold { @apply text-[14px] font-semibold; }
  .title-14-bold { @apply text-[14px] font-bold; }
  .title-12-regular { @apply text-[12px] font-normal; }
  .title-12-semibold { @apply text-[12px] font-semibold; }
  .title-12-bold { @apply text-[12px] font-bold; }
  .title-11-regular { @apply text-[11px] font-normal; }
  .title-11-semibold { @apply text-[11px] font-semibold; }
  .title-11-bold { @apply text-[11px] font-bold; }
  .title-10-regular { @apply text-[10px] font-normal; }
  .title-10-semibold { @apply text-[10px] font-semibold; }
  .title-10-bold { @apply text-[10px] font-bold; }
  .title-9-regular { @apply text-[9px] font-normal; }
  .title-8-regular { @apply text-[8px] font-normal; }

  /* Buttons */
  .btn-14-medium { @apply text-[14px] font-medium; }
  .btn-14-semibold { @apply text-[14px] font-semibold; }
  .btn-12-medium { @apply text-[12px] font-medium; }
  .btn-12-semibold { @apply text-[12px] font-semibold; }
  .btn-10-medium { @apply text-[10px] font-medium; }
  .btn-10-semibold { @apply text-[10px] font-semibold; }

  /* Body */
  .body-16-regular { @apply text-[16px] font-normal; }
  .body-16-semibold { @apply text-[16px] font-semibold; }
  .body-16-bold { @apply text-[16px] font-bold; }
  .body-14-regular { @apply text-[14px] font-normal; }
  .body-14-semibold { @apply text-[14px] font-semibold; }
  .body-14-bold { @apply text-[14px] font-bold; }
  .body-12-regular { @apply text-[12px] font-normal; }
  .body-12-semibold { @apply text-[12px] font-semibold; }
  .body-12-bold { @apply text-[12px] font-bold; }
  .body-11-regular { @apply text-[11px] font-normal; }
  .body-11-semibold { @apply text-[11px] font-semibold; }
  .body-11-bold { @apply text-[11px] font-bold; }
  .body-10-regular { @apply text-[10px] font-normal; }
  .body-10-semibold { @apply text-[10px] font-semibold; }
  .body-10-bold { @apply text-[10px] font-bold; }
}
