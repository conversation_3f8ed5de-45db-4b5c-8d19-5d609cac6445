import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { NotificationFilters } from '@/lib/types';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const statusFilters = searchParams.getAll('status');
    const typeFilters = searchParams.getAll('type');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    const filters: NotificationFilters = {
      status: statusFilters.length > 0 ? statusFilters as ('read' | 'unread')[] : undefined,
      type: typeFilters.length > 0 ? typeFilters as ('system' | 'report_update' | 'message' | 'alert')[] : undefined,
      limit,
      offset
    };
    
    const notifications = await DataService.getNotifications(request.user!.id, filters);
    const unreadCount = await DataService.getUnreadNotificationCount(request.user!.id);
    
    return NextResponse.json({
      success: true,
      data: notifications,
      unreadCount,
      pagination: {
        total: notifications.length,
        limit,
        offset,
        hasMore: notifications.length === limit
      }
    });
  } catch (error) {
    console.error('Notifications GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const PATCH = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { notificationId, action } = await request.json();
    
    if (!notificationId) {
      return NextResponse.json(
        { success: false, error: 'Notification ID is required' },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (action) {
      case 'mark_read':
        result = await DataService.markNotificationAsRead(notificationId);
        break;
      case 'mark_all_read':
        result = await DataService.markAllNotificationsAsRead(request.user!.id);
        break;
      default:
        result = await DataService.markNotificationAsRead(notificationId);
    }
    
    return NextResponse.json({
      success: true,
      data: result,
      message: 'Notification updated successfully'
    });
  } catch (error) {
    console.error('Notifications PATCH API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});