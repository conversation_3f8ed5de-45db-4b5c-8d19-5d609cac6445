// This script adds the Node.js runtime directive to API routes that use database operations
// Run with: pnpm tsx scripts/add-nodejs-runtime.ts

import fs from 'fs';
import path from 'path';

// Patterns that indicate a file needs Node.js runtime
const nodeJsPatterns = [
  'connectDB',
  'mongoose',
  'DataService',
  'mongodb',
  'getServerSession',
  'auth()',
  'createApiHandler'
];

// API routes directory
const apiDir = path.join(process.cwd(), 'src', 'app', 'api');

// Function to check if a file needs Node.js runtime
function needsNodeJsRuntime(content: string): boolean {
  return nodeJsPatterns.some(pattern => content.includes(pattern));
}

// Function to add Node.js runtime directive if needed
function addNodeJsRuntime(filePath: string): void {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Skip if already has runtime directive
  if (content.includes('export const runtime =')) {
    console.log(`✓ Already has runtime directive: ${filePath}`);
    return;
  }
  
  // Check if file needs Node.js runtime
  if (needsNodeJsRuntime(content)) {
    // Find the last import statement
    const importLines = content.split('\n').filter(line => line.trim().startsWith('import '));
    const lastImportIndex = content.lastIndexOf(importLines[importLines.length - 1]);
    const lastImportEndIndex = content.indexOf('\n', lastImportIndex) + 1;
    
    // Insert the runtime directive after the last import
    const newContent = 
      content.slice(0, lastImportEndIndex) + 
      '\nexport const runtime = \'nodejs\';\n' + 
      content.slice(lastImportEndIndex);
    
    fs.writeFileSync(filePath, newContent);
    console.log(`✓ Added Node.js runtime to: ${filePath}`);
  } else {
    console.log(`✓ No Node.js runtime needed: ${filePath}`);
  }
}

// Function to recursively process directories
function processDirectory(dirPath: string): void {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      processDirectory(fullPath);
    } else if (entry.name === 'route.ts' || entry.name === 'route.js') {
      addNodeJsRuntime(fullPath);
    }
  }
}

// Start processing
console.log('Adding Node.js runtime directives to API routes...');
processDirectory(apiDir);
console.log('Done!');