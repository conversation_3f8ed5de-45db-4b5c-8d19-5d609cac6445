import "./globals.css";
import { Urbanist } from "next/font/google";
import { Analytics } from '@vercel/analytics/react';
import { Toaster } from 'sonner';
import Link from "next/link";
import type { Metadata } from 'next';

const urbanist = Urbanist({
  weight: ["400", "500", "600", "700", "800"],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-urbanist',
  preload: true,
  fallback: ['Arial', 'sans-serif']
});

export const metadata: Metadata = {
  title: '7IRIS Whistleblower Platform',
  description: 'Secure, anonymous whistleblowing platform that ensures EU compliance, protects whistleblowers, and promotes corporate transparency and ethical reporting.',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png'
  },
  manifest: '/manifest.json'
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#1E4841'
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={urbanist.variable} dir="ltr">
            <body
        suppressHydrationWarning
        className={`${urbanist.className} antialiased min-h-screen flex flex-col`}
        style={{
          backgroundColor: 'var(--color-off-white)',
          color: 'var(--color-dark-green)'
        }}
        data-new-gr-c-s-check-loaded="skip"
        data-gr-ext-installed="skip"
      >
        <Link
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:p-4 focus:z-50 focus:top-0 focus:left-0 focus:outline-2 focus-visible:ring-ring/50 focus-visible:ring-[3px] rounded-md"
          style={{
            backgroundColor: 'var(--color-off-white)',
            color: 'var(--color-dark-green)',
            outlineColor: 'var(--color-dark-green)'
          }}
        >
          Skip to main content
        </Link>
        
        {/* Development tools wrapper */}
        {process.env.NODE_ENV === 'development' && (
          <div 
            className="fixed bottom-0 right-0 z-50 p-2 text-xs rounded-tl-md title-10-regular"
            style={{
              backgroundColor: 'var(--color-dark-gray)',
              color: 'var(--color-off-white)'
            }}
          >
            DEV MODE
          </div>
        )}
        
        {children}
        <Toaster 
          position="top-right"
          closeButton
          richColors
          theme="light"
        />
        <Analytics />
        {/* <script src="/structured-data.js" async></script> */}
      </body>
    </html>
  );
}
