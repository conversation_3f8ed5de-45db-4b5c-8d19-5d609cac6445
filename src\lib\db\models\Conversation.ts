import { Schema } from 'mongoose';
import { createModel } from '../utils';

const ConversationSchema = new Schema({
  reportId: { type: Schema.Types.ObjectId, ref: 'Report', required: true },
  participants: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  status: { 
    type: String, 
    enum: ['active', 'closed', 'archived'],
    default: 'active'
  },
  lastMessageAt: { type: Date },
  isEncrypted: { type: Boolean, default: true }
}, {
  timestamps: true
});

// Use the utility function to create the model safely
const Conversation = createModel('Conversation', ConversationSchema);

export default Conversation;