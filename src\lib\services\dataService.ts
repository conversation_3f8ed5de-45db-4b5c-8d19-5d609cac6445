import { Notification } from '@/lib/types';
import { notificationsData } from '@/lib/mockData/notificationData';

export class NotificationService {
  async getNotificationsByUserId(userId: string): Promise<Notification[]> {
    // In a real app, this would fetch from database
    // For now, return mock data
    return notificationsData.filter(notification => notification.userId === userId);
  }

  async getLegacyNotifications(): Promise<Notification[]> {
    // Return all mock notifications as fallback
    return notificationsData;
  }

  async markAsRead(notificationId: string): Promise<void> {
    // In a real app, this would update the database
    console.log(`Marking notification ${notificationId} as read`);
  }

  async markAllAsRead(userId: string): Promise<void> {
    // In a real app, this would update all user notifications in database
    console.log(`Marking all notifications for user ${userId} as read`);
  }
}

export const notificationService = new NotificationService();