"use client";
import Link from "next/link";
import Image from "next/image";
import { Inter } from "next/font/google";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { SOCIAL_LINKS, WHISTLEBLOWING_LINKS, SYSTEM_LINKS, FOOTER_LINKS } from "@/lib/mockData";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
} from "@/components/ui/form";
import { useState } from "react";
import { Label } from "@/components/ui/label";

const inter = Inter({
    weight: ["200", "400", "500", "600", "700", "800"],
    subsets: ['latin']
});

const newsletterSchema = z.object({
    email: z.string().email("Please enter a valid email address").min(1, "Email is required"),
});

type NewsletterFormValues = z.infer<typeof newsletterSchema>;

export default function Footer() {
    const [isSubmitting, setIsSubmitting] = useState(false);

    const form = useForm<NewsletterFormValues>({
        resolver: zodResolver(newsletterSchema),
        defaultValues: {
            email: "",
        },
    });

    const onSubmit = async (values: NewsletterFormValues) => {
        setIsSubmitting(true);
        try {
            const response = await fetch('/api/newsletter', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(values),
            });
            
            if (response.ok) {
                form.reset();
            }
        } catch (error) {
            console.error('Newsletter subscription failed:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <footer className={`flex flex-col gap-10 ${inter.className} font-normal w-full bg-[#ECF4E9] text-black px-4 sm:px-8 md:px-16`}>
            <div className="flex flex-col lg:flex-row pt-10 lg:pt-20 lg:pb-10 justify-between gap-10">
                <div className="flex flex-col gap-6 w-full lg:w-3/8">
                    <Link href="/">
                        <Image
                            src="/logo.svg"
                            alt="Logo"
                            width={83}
                            height={37}
                            className="h-auto w-25 rounded-full hover:scale-105 transition-transform duration-300"
                        />
                    </Link>
                    <p className="text-base">Stay up to date on the latest features and releases by joining our newsletter.</p>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col sm:flex-row gap-2" aria-labelledby="newsletter-title">
                            <h3 id="newsletter-title" className="sr-only">Newsletter Subscription</h3>
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <Label htmlFor="newsletter-email" className="sr-only">
                                            Enter your email address to subscribe to our newsletter
                                        </Label>
                                        <FormControl>
                                            <Input
                                                {...field}
                                                id="newsletter-email"
                                                type="email"
                                                placeholder="Enter your email"
                                                className="py-6 rounded-sm border-black"
                                                autoComplete="email"
                                                aria-describedby="newsletter-help"
                                                disabled={isSubmitting}
                                            />
                                        </FormControl>
                                        <div id="newsletter-help" className="sr-only">
                                            Subscribe to receive updates about new features and releases
                                        </div>
                                    </FormItem>
                                )}
                            />
                            <Button
                                type="submit"
                                variant="outline"
                                aria-label="Subscribe to newsletter"
                                className="px-8 py-6 text-base font-normal rounded-sm bg-transparent hover:bg-lime-200 hover:text-gray-900 border-1 border-black transition-all duration-300"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? "Subscribing..." : "Subscribe"}
                            </Button>
                        </form>
                    </Form>
                    <p className="text-xs mt-[-10]">By subscribing, you agree to our Privacy Policy and consent to receive updates from our company.</p>
                </div>
                <div className="lg:w-1/2 flex flex-col sm:flex-row justify-between gap-6 md:gap-4">
                    <div className="flex flex-col gap-2 md:gap-4">
                        <p className="font-semibold text-base text-center md:text-left">System</p>
                        <div className="flex flex-row sm:flex-col justify-between md:justify-start md:gap-4 text-sm flex-wrap">
                            {SYSTEM_LINKS.map(link => (
                                <Link key={link.href} href={link.href} className="hover:underline decoration-2">{link.text}</Link>
                            ))}
                        </div>
                    </div>
                    <div className="flex flex-col gap-2 md:gap-4 items-center md:items-start">
                        <p className="font-semibold text-base">Whistleblowing</p>
                        <div className="flex flex-col gap-2 md:gap-4 text-sm text-center md:text-left">
                            {WHISTLEBLOWING_LINKS.map(link => (
                                <Link key={link.href} href={link.href} className="hover:underline decoration-2">
                                    {link.text}
                                </Link>
                            ))}
                        </div>
                    </div>
                    <div className="flex flex-col gap-4">
                        <p className="font-semibold text-base text-center md:text-left">Follow Us</p>
                        <div className="flex flex-row sm:flex-col justify-center md:justify-start gap-4 text-sm flex-wrap">
                            {SOCIAL_LINKS.map(social => (
                                <div key={social.icon} className="flex gap-4 items-center">
                                    <Image
                                        src={`/desktop/shared/footer/${social.icon}.svg`}
                                        alt={`${social.name} Logo`}
                                        height={24}
                                        width={24}
                                        className=""
                                    />
                                    <Link href={social.href} className="hover:underline decoration-2">{social.name}</Link>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
            <div className="border-t-2 border-black"></div>
            <div className="flex flex-col sm:flex-row justify-between text-xs lg:text-sm gap-4">
                <p className="w-full lg:w-1/2 md:w-2/5 text-center md:text-left">© {new Date().getFullYear()} Created by Airdokan & Powerd by Webflow. All rights reserved.</p>
                <div className="flex flex-wrap justify-between md:gap-6 mb-6">
                    {FOOTER_LINKS.map(link => (
                        <Link key={link.href} href={link.href} className="border-b-2 border-black">
                            {link.text}
                        </Link>
                    ))}
                </div>
            </div>
        </footer>
    );
}