'use client';

import { useState, useEffect } from 'react';
import { UseSocketReturn } from '@/hooks/useSocket';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface SocketDebugPanelProps {
  socketData: UseSocketReturn;
  userId: string | null;
}

export function SocketDebugPanel({ socketData, userId }: SocketDebugPanelProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [healthReport, setHealthReport] = useState<{ status: string; issues: string[] }>({ status: 'healthy', issues: [] });

  useEffect(() => {
    const updateHealth = () => {
      const report = socketData.connectionHealth.getHealthReport();
      setHealthReport(report);
    };

    updateHealth();
    const interval = setInterval(updateHealth, 2000); // Update every 2 seconds

    return () => clearInterval(interval);
  }, [socketData.connectionHealth]);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="bg-slate-900 text-white border-slate-700 hover:bg-slate-800"
        >
          🔧 Socket Debug
        </Button>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'unstable': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="bg-slate-900 text-white border-slate-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Socket Debug Panel</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0 hover:bg-slate-800"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Connection Status */}
          <div className="flex items-center justify-between">
            <span>Connection:</span>
            <Badge variant={socketData.isConnected ? "default" : "destructive"}>
              {socketData.isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>

          {/* User Info */}
          <div className="flex items-center justify-between">
            <span>User ID:</span>
            <span className="text-slate-300 font-mono">{userId || 'Not set'}</span>
          </div>

          {/* Health Status */}
          <div className="flex items-center justify-between">
            <span>Health:</span>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(healthReport.status)}`} />
              <span className="capitalize">{healthReport.status}</span>
            </div>
          </div>

          {/* Connection Stats */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span>Reconnections:</span>
              <span className={socketData.connectionHealth.reconnectionCount > 3 ? 'text-red-400' : 'text-slate-300'}>
                {socketData.connectionHealth.reconnectionCount}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span>Stable:</span>
              <span className={socketData.connectionHealth.isStable ? 'text-green-400' : 'text-red-400'}>
                {socketData.connectionHealth.isStable ? 'Yes' : 'No'}
              </span>
            </div>

            {socketData.connectionHealth.lastConnectedAt && (
              <div className="flex items-center justify-between">
                <span>Last Connected:</span>
                <span className="text-slate-300">
                  {socketData.connectionHealth.lastConnectedAt.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>

          {/* Online Users */}
          <div className="flex items-center justify-between">
            <span>Online Users:</span>
            <span className="text-slate-300">{socketData.onlineUsers.size}</span>
          </div>

          {/* Typing Users */}
          <div className="flex items-center justify-between">
            <span>Active Conversations:</span>
            <span className="text-slate-300">{socketData.typingUsers.size}</span>
          </div>

          {/* Health Issues */}
          {healthReport.issues.length > 0 && (
            <div className="space-y-1">
              <span className="text-red-400 font-medium">Issues:</span>
              <div className="space-y-1 max-h-20 overflow-y-auto">
                {healthReport.issues.map((issue, index) => (
                  <div key={index} className="text-red-300 text-xs bg-red-900/20 p-1 rounded">
                    {issue}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="pt-2 border-t border-slate-700">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                const report = socketData.connectionHealth.getHealthReport();
                console.log('🔧 Socket Debug Report:', {
                  isConnected: socketData.isConnected,
                  userId,
                  connectionHealth: socketData.connectionHealth,
                  healthReport: report,
                  onlineUsers: Array.from(socketData.onlineUsers),
                  typingUsers: Object.fromEntries(
                    Array.from(socketData.typingUsers.entries()).map(([k, v]) => [k, Array.from(v)])
                  )
                });
              }}
              className="w-full text-xs hover:bg-slate-800"
            >
              📋 Log Full Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}