"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CalendarIcon, Clock } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";

const generateTimeSlots = (interval: number = 30, minTime: string = "09:00", maxTime: string = "17:00") => {
  const times: string[] = [];
  const [minHour, minMinute] = minTime.split(":").map(Number);
  const [maxHour, maxMinute] = maxTime.split(":").map(Number);
  
  let currentHour = minHour;
  let currentMinute = minMinute;
  
  while (currentHour < maxHour || (currentHour === maxHour && currentMinute <= maxMinute)) {
    times.push(
      `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`
    );
    
    currentMinute += interval;
    if (currentMinute >= 60) {
      currentHour += 1;
      currentMinute = 0;
    }
  }
  
  return times;
};

type TimePickerProps = {
  selected: string;
  onSelect: (value: string) => void;
  interval?: number;
  minTime?: string;
  maxTime?: string;
};

const TimePicker: React.FC<TimePickerProps> = ({
  selected,
  onSelect,
  interval = 30,
  minTime = "09:00",
  maxTime = "17:00"
}) => {
  const timeSlots = generateTimeSlots(interval, minTime, maxTime);

  return (
    <div className="p-2 h-64 overflow-y-auto">
      {timeSlots.map((time) => (
        <div
          key={time}
          className={`px-3 py-2 cursor-pointer bg-white hover:bg-gray-100 rounded ${
            selected === time ? "bg-[#1E4841] text-white" : ""
          }`}
          onClick={() => onSelect(time)}
        >
          {time}
        </div>
      ))}
    </div>
  );
};

const contactFormSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Please enter a valid business email address").min(1, "Email is required"),
  companyName: z.string().min(1, "Company name is required"), 
  phone: z.string().min(1, "Phone number is required"),
  preferredDate: z.date({message: "Preferred date is required"}),
  preferredTime: z.string().min(1, "Preferred time is required"),
  timeZone: z.string().min(1, "Time zone is required"),
  additionalNotes: z.string().optional(),
  consent: z.boolean().refine((value) => value === true, "Consent is required"),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

export default function ScheduleDemo() {
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      companyName: "",
      phone: "",
      preferredDate: undefined,
      preferredTime: "",
      timeZone: "",
      additionalNotes: "",
      consent: false,
    },
  });

  const onSubmit = async (values: ContactFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("Contact form submission:", values);
      setSuccess(true);
      form.reset();
    } catch {
      setError("Failed to send message. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="max-w-2xl mx-auto flex-1/2 border-none shadow-none hover:shadow-md bg-white">
        <CardContent className="p-8 text-center">
          <h3 className="text-xl font-semibold text-[#1E4841] mb-2">Scheduled Successfully!</h3>
          <p className="text-gray-600 mb-6">
            Thank you for contacting us. We&apos;ll get back to you within 24 hours.
          </p>
          <Button
            onClick={() => setSuccess(false)}
            className="bg-[#1E4841] hover:bg-[#1E4841]/90"
          >
            Schedule Another Demo
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-2xl mx-auto flex-1/2 border-none shadow-none hover:shadow-md bg-white">
      <CardHeader className="text-left text-[#242E2C] font-semibold text-xl">Select a Date & Time</CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6" noValidate>
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Full Name <span className="text-[#EF4444]">*</span></FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter your full name"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Business Email <span className="text-[#EF4444]">*</span></FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter your business email"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="companyName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Company Name <span className="text-[#EF4444]">*</span></FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter your company name"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Phone Number <span className="text-[#EF4444]">*</span></FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="tel"
                      placeholder="Enter your phone number"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="preferredDate" 
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="text-sm font-medium">Preferred Date <span className="text-[#EF4444]">*</span></FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                              !field.value ? 'bg-[#EFEFEF]' : ''
                            )}
                          >
                            {field.value ? (
                              format(field.value as Date, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-white" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date()
                          }
                          captionLayout="dropdown"
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="preferredTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Preferred Time <span className="text-[#EF4444]">*</span></FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                              !field.value ? 'bg-[#EFEFEF]' : ''
                            )}
                          >
                            {field.value ? (
                              field.value
                            ) : (
                              <span>Pick a time</span>
                            )}
                            <Clock className="ml-auto h-4 w-4" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[240px] p-0 bg-white" align="start">
                        <TimePicker
                          selected={field.value}
                          onSelect={field.onChange}
                          interval={30}
                          minTime="09:00"
                          maxTime="17:00"
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="timeZone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Time Zone <span className="text-[#EF4444]">*</span></FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isLoading}
                    >
                      <SelectTrigger className={`w-full ${!field.value ? 'bg-[#EFEFEF]' : ''}`}>
                        <SelectValue placeholder="Select your time zone" />
                      </SelectTrigger>
                      <SelectContent className="bg-white">
                        <SelectItem value="PST">Pacific Time (PST)</SelectItem>
                        <SelectItem value="MST">Mountain Time (MST)</SelectItem>
                        <SelectItem value="CST">Central Time (CST)</SelectItem>
                        <SelectItem value="EST">Eastern Time (EST)</SelectItem>
                        <SelectItem value="GMT">Greenwich Mean Time (GMT)</SelectItem>
                        <SelectItem value="CET">Central European Time (CET)</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="additionalNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Tell us about your specific requirements or questions."
                      className="min-h-[80px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white py-3"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Scheduling...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  Schedule Demo
                </div>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}