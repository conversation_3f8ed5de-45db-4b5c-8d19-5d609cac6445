import { NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { User } from '@/lib/db/models';

export const runtime = 'nodejs';

export async function GET() {
  try {
    await connectDB();
    
    const user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Type assertion to access user properties
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const userData = user as any;
    
    return NextResponse.json({
      success: true,
      data: {
        email: userData.email,
        hashedPassword: userData.hashedPassword,
        hashLength: userData.hashedPassword?.length,
        role: userData.role,
        isActive: userData.isActive,
        companyId: userData.companyId,
        firstName: userData.firstName,
        lastName: userData.lastName
      }
    });
  } catch (error) {
    console.error('Debug user API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}