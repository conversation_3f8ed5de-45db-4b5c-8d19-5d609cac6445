import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import connectDB from '../src/lib/db/mongodb';
import { User, Company, Report, Notification, Conversation, Message, Blog, PricingPlan } from '../src/lib/db/models';
import { encryptMessage } from '../src/lib/encryption/messageEncryption';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Sample pricing plans data
const seedPricingPlans = [
  {
    name: 'Starter',
    price: 99,
    features: [
      'Secure anonymous reporting',
      'Basic case management tools',
      'Automated case tracking & audit logs',
      'Customizable intake forms',
      'Whistleblower two-way messaging',
      'Basic analytics dashboard'
    ],
    order: 1,
    isActive: true,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z')
  },
  {
    name: 'Professional',
    price: 169,
    features: [
      'Everything in Starter plus:',
      'Advanced case routing & AI risk scoring',
      'Custom workflows & approval processes',
      'Surveys & employee engagement tools',
      'Automated fraud detection & Analysis',
      'Customizable alerts & notifications',
      'HR & compliance assistant'
    ],
    order: 2,
    isActive: true,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z')
  },
  {
    name: 'Enterprise',
    price: 499,
    features: [
      'Everything in Professional plus:',
      'Phone hotline & multilingual support',
      'Unlimited investigators & case managers',
      'Custom server locations',
      'Dedicated success manager',
      'Enterprise-level compliance',
      'Advanced analytics & reporting'
    ],
    order: 3,
    isActive: true,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z')
  }
];

// Sample blog posts data
const seedBlogPosts = [
  {
    image: '/desktop/blog/featured/f-blog1.jpg',
    category: 'Compliance Updates',
    date: 'June 1, 2025',
    title: 'New EU Whistleblower Protection Directive: What Organizations Need to Know',
    description: 'The latest updates to the EU Whistleblower Protection Directive bring significant changes to compliance requirements. Learn how these changes impact your organization and what steps you need to take to ensure compliance.',
    author: {
      image: '/desktop/blog/authors/Dr. Elizabeth Morgan.jpg',
      name: 'Dr. Elizabeth Morgan',
      initials: 'EM'
    },
    readTime: '8 min read',
    slug: 'new-eu-whistleblower-protection-directive',
    featured: true,
    tags: ['EU Directive', 'Whistleblower Protection', 'Compliance', 'Regulatory Updates'],
    content: 'The European Union has recently introduced significant updates to the Whistleblower Protection Directive, marking a pivotal shift in how organizations across member states must approach whistleblowing mechanisms and protections.',
    createdAt: new Date('2024-06-01T00:00:00Z'),
    updatedAt: new Date('2024-06-01T00:00:00Z')
  },
  {
    image: '/desktop/blog/featured/f-blog2.jpg',
    category: 'Best Practices',
    date: 'May 28, 2025',
    title: 'Building a Culture of Ethical Reporting: 5 Strategies That Work',
    description: 'Creating an environment where employees feel safe to report misconduct is crucial for organizational integrity. Discover five proven strategies to foster a culture of ethical reporting and transparency.',
    author: {
      image: '/desktop/blog/authors/James Chen, CCEP.jpg',
      name: 'James Chen, CCEP',
      initials: 'JC'
    },
    readTime: '8 min read',
    slug: 'building-culture-ethical-reporting',
    featured: true,
    tags: ['Ethical Culture', 'Reporting Channels', 'Anti-Retaliation', 'Corporate Ethics'],
    content: 'Creating a culture where employees feel empowered to report ethical concerns is essential for maintaining organizational integrity and preventing misconduct. Here are five proven strategies that can help foster such an environment.',
    createdAt: new Date('2024-05-28T00:00:00Z'),
    updatedAt: new Date('2024-05-28T00:00:00Z')
  },
  {
    image: '/desktop/blog/featured/f-blog3.jpg',
    category: 'Case Studies',
    date: 'May 15, 2025',
    title: 'How Acme Corporation Transformed Their Compliance Program',
    description: 'After facing regulatory challenges, Acme Corporation revamped their compliance program with remarkable results. This case study examines their journey, the solutions implemented, and the measurable outcomes achieved.',
    author: {
      image: '/desktop/blog/authors/Olivia Washington.jpg',
      name: 'Olivia Washington',
      initials: 'OW'
    },
    readTime: '12 min read',
    slug: 'how-acme-corporation-transformed-compliance',
    featured: true,
    tags: ['Case Study', 'Compliance Program', 'Transformation', 'Best Practices'],
    content: 'Acme Corporation, a global manufacturing company with over 10,000 employees, faced significant compliance challenges in 2023. This case study examines how they transformed their approach to create a model compliance program.',
    createdAt: new Date('2024-05-15T00:00:00Z'),
    updatedAt: new Date('2024-05-15T00:00:00Z')
  }
];

// Sample company data
const companies = [
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef12345'),
    name: 'TechCorp Industries',
    domain: 'techcorp.com',
    industry: 'Technology',
    size: 'Large',
    address: {
      street: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA'
    },
    contactInfo: {
      phone: '******-0123',
      email: '<EMAIL>',
      website: 'https://techcorp.com'
    },
    subscription: {
      plan: 'enterprise',
      status: 'active',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2025-01-01'),
      maxUsers: 1000,
      features: ['advanced_analytics', 'custom_branding', 'api_access']
    },
    settings: {
      allowAnonymousReports: true,
      requireApproval: false,
      retentionPeriod: 2555, // 7 years in days
      encryptionEnabled: true
    },
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-15')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef12346'),
    name: 'Global Manufacturing Co',
    domain: 'globalmfg.com',
    industry: 'Manufacturing',
    size: 'Large',
    address: {
      street: '456 Industrial Blvd',
      city: 'Detroit',
      state: 'MI',
      zipCode: '48201',
      country: 'USA'
    },
    contactInfo: {
      phone: '******-0456',
      email: '<EMAIL>',
      website: 'https://globalmfg.com'
    },
    subscription: {
      plan: 'professional',
      status: 'active',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2025-02-01'),
      maxUsers: 500,
      features: ['analytics', 'custom_workflows']
    },
    settings: {
      allowAnonymousReports: true,
      requireApproval: true,
      retentionPeriod: 1825, // 5 years in days
      encryptionEnabled: true
    },
    isActive: true,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2025-01-15')
  }
];

// Sample users data
const users = [
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef00001'),
    email: '<EMAIL>',
    firstName: 'Emily',
    lastName: 'Johnson',
    role: 'admin',
    companyId: new mongoose.Types.ObjectId('65f1234567890abcdef12345'),
    hashedPassword: '', // Will be set below
    isActive: true,
    emailVerified: true,
    lastLogin: new Date('2025-01-15T08:00:00Z'),
    lastActive: new Date('2025-01-15T10:30:00Z'),
    preferences: {
      language: 'en',
      notifications: {
        email: true,
        push: true,
        sms: false
      },
      theme: 'light'
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-15')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
    email: '<EMAIL>',
    firstName: 'Alexandra',
    lastName: 'Smith',
    role: 'investigator',
    companyId: new mongoose.Types.ObjectId('65f1234567890abcdef12345'),
    hashedPassword: '', // Will be set below
    isActive: true,
    emailVerified: true,
    lastLogin: new Date('2025-01-15T07:30:00Z'),
    lastActive: new Date('2025-01-15T10:15:00Z'),
    preferences: {
      language: 'en',
      notifications: {
        email: true,
        push: true,
        sms: true
      },
      theme: 'light'
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-15')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef00003'),
    email: '<EMAIL>',
    firstName: 'Michael',
    lastName: 'Johnson',
    role: 'investigator',
    companyId: new mongoose.Types.ObjectId('65f1234567890abcdef12345'),
    hashedPassword: '', // Will be set below
    isActive: true,
    emailVerified: true,
    lastLogin: new Date('2025-01-14T16:00:00Z'),
    lastActive: new Date('2025-01-15T09:45:00Z'),
    preferences: {
      language: 'en',
      notifications: {
        email: true,
        push: false,
        sms: false
      },
      theme: 'dark'
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-14')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'whistleblower',
    companyId: new mongoose.Types.ObjectId('65f1234567890abcdef12345'),
    hashedPassword: '', // Will be set below
    isActive: true,
    emailVerified: true,
    lastLogin: new Date('2025-01-15T09:00:00Z'),
    lastActive: new Date('2025-01-15T10:30:00Z'),
    preferences: {
      language: 'en',
      notifications: {
        email: true,
        push: true,
        sms: false
      },
      theme: 'light'
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-15')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'whistleblower',
    companyId: new mongoose.Types.ObjectId('65f1234567890abcdef12345'),
    hashedPassword: '', // Will be set below
    isActive: true,
    emailVerified: true,
    lastLogin: new Date('2025-01-14T14:30:00Z'),
    lastActive: new Date('2025-01-15T08:15:00Z'),
    preferences: {
      language: 'en',
      notifications: {
        email: true,
        push: true,
        sms: false
      },
      theme: 'light'
    },
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date('2025-01-14')
  },
  // Global Manufacturing Co users
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef00006'),
    email: '<EMAIL>',
    firstName: 'Robert',
    lastName: 'Wilson',
    role: 'admin',
    companyId: new mongoose.Types.ObjectId('65f1234567890abcdef12346'),
    hashedPassword: '', // Will be set below
    isActive: true,
    emailVerified: true,
    lastLogin: new Date('2025-01-15T07:00:00Z'),
    lastActive: new Date('2025-01-15T09:30:00Z'),
    preferences: {
      language: 'en',
      notifications: {
        email: true,
        push: true,
        sms: true
      },
      theme: 'light'
    },
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2025-01-15')
  }
];

// Sample reports data
const reports = [
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10001'),
    reportId: 'WB-2025-0012',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    title: 'Potential accounting irregularities in Q1 reports',
    description: 'Observed discrepancies in financial reporting that may indicate fraudulent activity. Multiple entries show inconsistent patterns and unexplained variances.',
    category: 'Financial',
    priority: 'High',
    status: 'Awaiting Response',
    isAnonymous: false,
    progress: 65,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
    createdAt: new Date('2025-01-10T10:00:00Z'),
    updatedAt: new Date('2025-01-14T15:30:00Z'),
    incidentDate: new Date('2025-01-08T00:00:00Z'),
    location: 'Accounting Department, Floor 3',
    tags: ['financial', 'fraud', 'urgent'],
    metadata: {
      submissionMethod: 'web',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10002'),
    reportId: 'WB-2025-0011',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    title: 'Workplace safety concerns in manufacturing plant B',
    description: 'Multiple safety violations observed that could lead to serious injuries. Equipment guards are missing and safety protocols are not being followed.',
    category: 'Workplace Safety',
    priority: 'Critical',
    status: 'Under Review',
    isAnonymous: false,
    progress: 45,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00003'),
    createdAt: new Date('2025-01-03T14:20:00Z'),
    updatedAt: new Date('2025-01-12T11:15:00Z'),
    incidentDate: new Date('2025-01-01T00:00:00Z'),
    location: 'Manufacturing Plant B',
    tags: ['safety', 'manufacturing', 'critical']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10003'),
    reportId: 'WB-2025-0010',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
    title: 'Inappropriate conduct by senior manager',
    description: 'Reported inappropriate behavior and harassment by senior management. Multiple incidents of unprofessional conduct witnessed.',
    category: 'Harassment',
    priority: 'High',
    status: 'Under Review',
    isAnonymous: false,
    progress: 35,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
    createdAt: new Date('2024-12-28T09:30:00Z'),
    updatedAt: new Date('2025-01-05T16:45:00Z'),
    incidentDate: new Date('2024-12-25T00:00:00Z'),
    location: 'Corporate Office, Floor 5',
    tags: ['harassment', 'management', 'urgent']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10004'),
    reportId: 'WB-2025-0009',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    title: 'Potential data privacy breach in customer databases',
    description: 'Suspected unauthorized access to customer personal information. Security logs show unusual access patterns.',
    category: 'Data Privacy',
    priority: 'Critical',
    status: 'Under Review',
    isAnonymous: false,
    progress: 50,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00003'),
    createdAt: new Date('2024-12-15T11:00:00Z'),
    updatedAt: new Date('2025-01-08T13:20:00Z'),
    incidentDate: new Date('2024-12-10T00:00:00Z'),
    location: 'IT Department',
    tags: ['data-privacy', 'security', 'critical']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10005'),
    reportId: 'WB-2025-0008',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    title: 'Conflict of interest in vendor selection process',
    description: 'Observed potential conflicts of interest in procurement decisions. Personal relationships may be influencing vendor selection.',
    category: 'Ethics Violation',
    priority: 'Medium',
    status: 'Resolved',
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
    createdAt: new Date('2024-12-02T08:15:00Z'),
    updatedAt: new Date('2024-12-30T17:00:00Z'),
    incidentDate: new Date('2024-11-28T00:00:00Z'),
    location: 'Procurement Department',
    tags: ['ethics', 'procurement', 'resolved']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10006'),
    reportId: 'WB-2025-0007',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
    title: 'Suspected misuse of company resources by department head',
    description: 'Department head using company resources for personal benefit. Company equipment and time being used for personal projects.',
    category: 'Ethics Violation',
    priority: 'Medium',
    status: 'Resolved',
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00003'),
    createdAt: new Date('2024-11-18T13:45:00Z'),
    updatedAt: new Date('2024-12-22T10:30:00Z'),
    incidentDate: new Date('2024-11-15T00:00:00Z'),
    location: 'Marketing Department',
    tags: ['ethics', 'resources', 'resolved']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10007'),
    reportId: 'WB-2025-0006',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    title: 'Environmental compliance concerns at production facility',
    description: 'Potential environmental violations at manufacturing site. Waste disposal procedures not being followed properly.',
    category: 'Workplace Safety',
    priority: 'Medium',
    status: 'Resolved',
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
    createdAt: new Date('2024-11-05T16:20:00Z'),
    updatedAt: new Date('2024-12-10T14:15:00Z'),
    incidentDate: new Date('2024-11-01T00:00:00Z'),
    location: 'Production Facility A',
    tags: ['environment', 'compliance', 'resolved']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10008'),
    reportId: 'WB-2025-0005',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
    title: 'Fraudulent expense claims by department head',
    description: 'Suspicious expense claims that appear to be fraudulent. Multiple claims for non-business related expenses.',
    category: 'Financial',
    priority: 'High',
    status: 'Resolved',
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00003'),
    createdAt: new Date('2024-10-28T12:00:00Z'),
    updatedAt: new Date('2024-12-10T09:45:00Z'),
    incidentDate: new Date('2024-10-20T00:00:00Z'),
    location: 'Finance Department',
    tags: ['financial', 'fraud', 'resolved']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10009'),
    reportId: 'WB-2025-0004',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    title: 'Misuse of company resources for personal gain',
    description: 'Employee using company equipment and time for personal projects. Observed multiple instances of personal use during work hours.',
    category: 'Ethics Violation',
    priority: 'Medium',
    status: 'New',
    isAnonymous: false,
    progress: 0,
    createdAt: new Date('2025-01-15T14:30:00Z'),
    updatedAt: new Date('2025-01-15T14:30:00Z'),
    incidentDate: new Date('2025-01-12T00:00:00Z'),
    location: 'Engineering Department',
    tags: ['ethics', 'resources', 'new']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10010'),
    reportId: 'WB-2025-0003',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
    title: 'Violation of customer data protection policies',
    description: 'Improper handling and storage of customer personal data. Data being stored in unsecured locations.',
    category: 'Data Privacy',
    priority: 'Critical',
    status: 'Awaiting Response',
    isAnonymous: false,
    progress: 75,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
    createdAt: new Date('2025-01-08T10:15:00Z'),
    updatedAt: new Date('2025-01-13T16:20:00Z'),
    incidentDate: new Date('2025-01-05T00:00:00Z'),
    location: 'Customer Service Department',
    tags: ['data-privacy', 'customer', 'urgent']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10011'),
    reportId: 'WB-2025-0002',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    title: 'Unsafe working conditions in warehouse facility',
    description: 'Multiple safety hazards observed in warehouse operations. Blocked emergency exits and improper storage of hazardous materials.',
    category: 'Workplace Safety',
    priority: 'High',
    status: 'New',
    isAnonymous: false,
    progress: 10,
    createdAt: new Date('2024-12-22T11:45:00Z'),
    updatedAt: new Date('2025-01-06T08:30:00Z'),
    incidentDate: new Date('2024-12-20T00:00:00Z'),
    location: 'Warehouse B',
    tags: ['safety', 'warehouse', 'hazards']
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef10012'),
    reportId: 'WB-2025-0001',
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
    title: 'Bribery and corruption in procurement process',
    description: 'Evidence of bribery in vendor selection and contract awards. Suspicious payments and preferential treatment observed.',
    category: 'Ethics Violation',
    priority: 'Critical',
    status: 'Resolved',
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: new mongoose.Types.ObjectId('65f1234567890abcdef00003'),
    createdAt: new Date('2024-11-15T09:20:00Z'),
    updatedAt: new Date('2024-12-30T15:45:00Z'),
    incidentDate: new Date('2024-11-10T00:00:00Z'),
    location: 'Procurement Department',
    tags: ['corruption', 'bribery', 'resolved']
  }
];

// Sample conversations
const conversations = [
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef20001'),
    reportId: new mongoose.Types.ObjectId('65f1234567890abcdef10001'),
    participants: [
      new mongoose.Types.ObjectId('65f1234567890abcdef00004'), // John Doe (whistleblower)
      new mongoose.Types.ObjectId('65f1234567890abcdef00002')  // Alexandra Smith (investigator)
    ],
    status: 'active',
    isEncrypted: true,
    createdAt: new Date('2025-01-10T10:30:00Z'),
    updatedAt: new Date('2025-01-15T09:22:00Z'),
    lastMessageAt: new Date('2025-01-15T09:22:00Z')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef20002'),
    reportId: new mongoose.Types.ObjectId('65f1234567890abcdef10002'),
    participants: [
      new mongoose.Types.ObjectId('65f1234567890abcdef00004'), // John Doe (whistleblower)
      new mongoose.Types.ObjectId('65f1234567890abcdef00003')  // Michael Johnson (investigator)
    ],
    status: 'active',
    isEncrypted: true,
    createdAt: new Date('2025-01-03T14:45:00Z'),
    updatedAt: new Date('2025-01-14T16:30:00Z'),
    lastMessageAt: new Date('2025-01-14T16:30:00Z')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef20003'),
    reportId: new mongoose.Types.ObjectId('65f1234567890abcdef10003'),
    participants: [
      new mongoose.Types.ObjectId('65f1234567890abcdef00005'), // Jane Smith (whistleblower)
      new mongoose.Types.ObjectId('65f1234567890abcdef00002')  // Alexandra Smith (investigator)
    ],
    status: 'active',
    isEncrypted: true,
    createdAt: new Date('2024-12-28T10:00:00Z'),
    updatedAt: new Date('2025-01-13T14:15:00Z'),
    lastMessageAt: new Date('2025-01-13T14:15:00Z')
  }
];

// Sample notifications
const notifications = [
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef30001'),
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    type: 'message',
    title: 'New Message from Investigator',
    message: 'Investigator Alexandra has responded to your report WB-2025-0012',
    status: 'unread',
    priority: 'high',
    actionUrl: '/dashboard/whistleblower/secure-message?conversation=65f1234567890abcdef20001',
    reportId: new mongoose.Types.ObjectId('65f1234567890abcdef10001'),
    createdAt: new Date('2025-01-15T09:22:00Z'),
    updatedAt: new Date('2025-01-15T09:22:00Z'),
    metadata: {
      source: 'investigator_response',
      category: 'report_update'
    }
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef30002'),
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    type: 'report_update',
    title: 'Report Status Updated',
    message: 'Your report WB-2025-0011 status has been updated to Under Review',
    status: 'unread',
    priority: 'medium',
    actionUrl: '/dashboard/whistleblower/my-reports?report=WB-2025-0011',
    reportId: new mongoose.Types.ObjectId('65f1234567890abcdef10002'),
    createdAt: new Date('2025-01-14T11:15:00Z'),
    updatedAt: new Date('2025-01-14T11:15:00Z'),
    metadata: {
      source: 'status_change',
      category: 'report_update',
      previousStatus: 'New',
      newStatus: 'Under Review'
    }
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef30003'),
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
    type: 'message',
    title: 'New Message from Investigator',
    message: 'Investigator Alexandra has sent you a message regarding report WB-2025-0010',
    status: 'read',
    priority: 'medium',
    actionUrl: '/dashboard/whistleblower/secure-message?conversation=65f1234567890abcdef20003',
    reportId: new mongoose.Types.ObjectId('65f1234567890abcdef10003'),
    readAt: new Date('2025-01-13T15:00:00Z'),
    createdAt: new Date('2025-01-13T14:15:00Z'),
    updatedAt: new Date('2025-01-13T15:00:00Z'),
    metadata: {
      source: 'investigator_message',
      category: 'communication'
    }
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef30004'),
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
    type: 'system',
    title: 'Welcome to the Whistleblower Platform',
    message: 'Your account has been successfully created. You can now submit reports and communicate securely with investigators.',
    status: 'read',
    priority: 'low',
    actionUrl: '/dashboard/whistleblower',
    readAt: new Date('2024-01-02T10:00:00Z'),
    createdAt: new Date('2024-01-01T12:00:00Z'),
    updatedAt: new Date('2024-01-02T10:00:00Z'),
    metadata: {
      source: 'system',
      category: 'welcome'
    }
  },
  // Admin notifications
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef30005'),
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00001'),
    type: 'alert',
    title: 'High Priority Report Submitted',
    message: 'A new high priority report WB-2025-0012 has been submitted and requires immediate attention',
    status: 'unread',
    priority: 'high',
    actionUrl: '/dashboard/admin/reports?report=WB-2025-0012',
    reportId: new mongoose.Types.ObjectId('65f1234567890abcdef10001'),
    createdAt: new Date('2025-01-10T10:05:00Z'),
    updatedAt: new Date('2025-01-10T10:05:00Z'),
    metadata: {
      source: 'report_submission',
      category: 'admin_alert'
    }
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef30006'),
    userId: new mongoose.Types.ObjectId('65f1234567890abcdef00001'),
    type: 'system',
    title: 'New User Registration',
    message: 'A new user has registered and requires account verification',
    status: 'read',
    priority: 'medium',
    actionUrl: '/dashboard/admin/users',
    readAt: new Date('2024-02-16T09:00:00Z'),
    createdAt: new Date('2024-02-15T16:30:00Z'),
    updatedAt: new Date('2024-02-16T09:00:00Z'),
    metadata: {
      source: 'user_registration',
      category: 'admin_notification'
    }
  }
];

// Sample pricing plans
const pricingPlans = [
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef40001'),
    name: 'Starter',
    description: 'Perfect for small organizations getting started with whistleblowing compliance',
    price: {
      monthly: 99,
      yearly: 990,
      currency: 'USD'
    },
    features: [
      'Up to 50 users',
      'Basic reporting tools',
      'Email notifications',
      'Standard support',
      'Basic analytics',
      '1GB storage'
    ],
    limits: {
      maxUsers: 50,
      maxReports: 100,
      storageGB: 1,
      apiCalls: 1000
    },
    isActive: true,
    isPopular: false,
    order: 1,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-01')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef40002'),
    name: 'Professional',
    description: 'Advanced features for growing organizations with comprehensive compliance needs',
    price: {
      monthly: 299,
      yearly: 2990,
      currency: 'USD'
    },
    features: [
      'Up to 500 users',
      'Advanced reporting & analytics',
      'Custom workflows',
      'Priority support',
      'Advanced encryption',
      '10GB storage',
      'API access',
      'Custom branding'
    ],
    limits: {
      maxUsers: 500,
      maxReports: 1000,
      storageGB: 10,
      apiCalls: 10000
    },
    isActive: true,
    isPopular: true,
    order: 2,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-01')
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef40003'),
    name: 'Enterprise',
    description: 'Complete solution for large enterprises with advanced security and compliance requirements',
    price: {
      monthly: 999,
      yearly: 9990,
      currency: 'USD'
    },
    features: [
      'Unlimited users',
      'Enterprise analytics',
      'Custom integrations',
      'Dedicated support',
      'Advanced security',
      'Unlimited storage',
      'Full API access',
      'White-label solution',
      'SLA guarantee',
      'On-premise deployment'
    ],
    limits: {
      maxUsers: -1, // Unlimited
      maxReports: -1, // Unlimited
      storageGB: -1, // Unlimited
      apiCalls: -1 // Unlimited
    },
    isActive: true,
    isPopular: false,
    order: 3,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2025-01-01')
  }
];

// Sample blog posts
const blogPosts = [
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef50001'),
    title: 'The Importance of Whistleblower Protection in Modern Organizations',
    slug: 'importance-whistleblower-protection-modern-organizations',
    excerpt: 'Learn why establishing robust whistleblower protection programs is crucial for organizational integrity and compliance.',
    content: `
      <h2>Introduction</h2>
      <p>In today's complex business environment, organizations face unprecedented challenges in maintaining ethical standards and regulatory compliance. Whistleblower protection programs have emerged as a critical component of effective corporate governance.</p>
      
      <h2>Key Benefits</h2>
      <ul>
        <li>Early detection of misconduct</li>
        <li>Reduced legal and financial risks</li>
        <li>Enhanced organizational culture</li>
        <li>Improved stakeholder trust</li>
      </ul>
      
      <h2>Implementation Best Practices</h2>
      <p>Successful whistleblower programs require careful planning, clear policies, and ongoing commitment from leadership. Organizations should focus on creating safe reporting channels, ensuring confidentiality, and establishing fair investigation processes.</p>
    `,
    author: {
      name: 'Dr. Sarah Johnson',
      title: 'Compliance Expert',
      bio: 'Dr. Johnson has over 15 years of experience in corporate compliance and ethics.'
    },
    category: 'Compliance',
    tags: ['whistleblower', 'compliance', 'ethics', 'governance'],
    featured: true,
    published: true,
    publishedAt: new Date('2025-01-10T10:00:00Z'),
    createdAt: new Date('2025-01-08T14:30:00Z'),
    updatedAt: new Date('2025-01-10T10:00:00Z'),
    seo: {
      metaTitle: 'Whistleblower Protection: Essential for Modern Organizations',
      metaDescription: 'Discover why whistleblower protection programs are crucial for organizational integrity, compliance, and risk management.',
      keywords: ['whistleblower protection', 'compliance', 'corporate governance', 'ethics']
    }
  },
  {
    _id: new mongoose.Types.ObjectId('65f1234567890abcdef50002'),
    title: 'Building Trust Through Transparent Reporting Systems',
    slug: 'building-trust-transparent-reporting-systems',
    excerpt: 'Explore how transparent and secure reporting systems can build trust between employees and management.',
    content: `
      <h2>The Foundation of Trust</h2>
      <p>Trust is the cornerstone of any successful organization. When employees feel safe to report concerns without fear of retaliation, it creates a culture of transparency and accountability.</p>
      
      <h2>Key Elements of Effective Reporting Systems</h2>
      <ul>
        <li>Anonymous reporting options</li>
        <li>Multiple reporting channels</li>
        <li>Clear communication about the process</li>
        <li>Regular updates on investigation progress</li>
      </ul>
    `,
    author: {
      name: 'Michael Chen',
      title: 'HR Director',
      bio: 'Michael specializes in organizational development and employee relations.'
    },
    category: 'Best Practices',
    tags: ['trust', 'transparency', 'reporting', 'culture'],
    featured: false,
    published: true,
    publishedAt: new Date('2025-01-05T09:00:00Z'),
    createdAt: new Date('2025-01-03T11:15:00Z'),
    updatedAt: new Date('2025-01-05T09:00:00Z'),
    seo: {
      metaTitle: 'Building Trust Through Transparent Reporting Systems',
      metaDescription: 'Learn how transparent reporting systems build trust and improve organizational culture.',
      keywords: ['transparent reporting', 'trust building', 'employee relations', 'organizational culture']
    }
  }
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Connected to database');

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await Promise.all([
      Company.deleteMany({}),
      User.deleteMany({}),
      Report.deleteMany({}),
      Notification.deleteMany({}),
      Conversation.deleteMany({}),
      Message.deleteMany({}),
      Blog.deleteMany({}),
      PricingPlan.deleteMany({})
    ]);
    console.log('✅ Existing data cleared');

    // Hash passwords for users
    console.log('🔐 Hashing passwords...');
    const saltRounds = 12;
    for (const user of users) {
      user.hashedPassword = await bcrypt.hash('password123', saltRounds);
    }
    console.log('✅ Passwords hashed');

    // Seed companies
    console.log('🏢 Seeding companies...');
    await Company.insertMany(companies);
    console.log(`✅ Seeded ${companies.length} companies`);

    // Seed users
    console.log('👥 Seeding users...');
    await User.insertMany(users);
    console.log(`✅ Seeded ${users.length} users`);

    // Seed reports
    console.log('📋 Seeding reports...');
    await Report.insertMany(reports);
    console.log(`✅ Seeded ${reports.length} reports`);

    // Seed conversations
    console.log('💬 Seeding conversations...');
    await Conversation.insertMany(conversations);
    console.log(`✅ Seeded ${conversations.length} conversations`);

    // Seed messages with encryption
    console.log('📨 Seeding messages...');
    const messages = [];
    
    // Messages for conversation 1 (WB-2025-0012)
    const msg1Content = "Thank you for your report regarding the accounting irregularities. We take these matters very seriously and have begun our investigation. Could you please provide more specific details about the discrepancies you observed?";
    const msg1Encrypted = encryptMessage(msg1Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60001'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20001'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00002'), // Alexandra
      content: msg1Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg1Encrypted.iv,
        contentTag: msg1Encrypted.tag
      },
      readBy: [],
      createdAt: new Date('2025-01-11T09:00:00Z'),
      updatedAt: new Date('2025-01-11T09:00:00Z')
    });

    const msg2Content = "I noticed several entries in the Q1 financial reports that don't match the supporting documentation. Specifically, there are revenue entries that appear to be inflated by approximately 15-20%. I have screenshots of the discrepancies if needed.";
    const msg2Encrypted = encryptMessage(msg2Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60002'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20001'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'), // John
      content: msg2Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg2Encrypted.iv,
        contentTag: msg2Encrypted.tag
      },
      readBy: [{
        userId: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
        readAt: new Date('2025-01-12T10:30:00Z')
      }],
      createdAt: new Date('2025-01-11T14:30:00Z'),
      updatedAt: new Date('2025-01-11T14:30:00Z')
    });

    const msg3Content = "Thank you for the additional details. The screenshots would be very helpful. Please upload them securely through this platform. We will also need to schedule a confidential interview to discuss this matter further. Are you available next week?";
    const msg3Encrypted = encryptMessage(msg3Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60003'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20001'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00002'), // Alexandra
      content: msg3Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg3Encrypted.iv,
        contentTag: msg3Encrypted.tag
      },
      readBy: [],
      createdAt: new Date('2025-01-15T09:22:00Z'),
      updatedAt: new Date('2025-01-15T09:22:00Z')
    });

    // Messages for conversation 2 (WB-2025-0011)
    const msg4Content = "We have received your safety report and are treating it as high priority. Our safety team will be conducting an inspection of Manufacturing Plant B tomorrow. Can you provide more details about the specific safety violations you observed?";
    const msg4Encrypted = encryptMessage(msg4Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60004'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20002'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00003'), // Michael
      content: msg4Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg4Encrypted.iv,
        contentTag: msg4Encrypted.tag
      },
      readBy: [{
        userId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'),
        readAt: new Date('2025-01-04T10:00:00Z')
      }],
      createdAt: new Date('2025-01-04T08:30:00Z'),
      updatedAt: new Date('2025-01-04T08:30:00Z')
    });

    const msg5Content = "The main issues I observed were: 1) Missing safety guards on several machines, 2) Blocked emergency exits due to equipment placement, 3) Workers not wearing required PPE, and 4) Improper storage of chemicals near heat sources. I can provide photos if needed.";
    const msg5Encrypted = encryptMessage(msg5Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60005'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20002'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00004'), // John
      content: msg5Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg5Encrypted.iv,
        contentTag: msg5Encrypted.tag
      },
      readBy: [{
        userId: new mongoose.Types.ObjectId('65f1234567890abcdef00003'),
        readAt: new Date('2025-01-05T09:15:00Z')
      }],
      createdAt: new Date('2025-01-04T16:45:00Z'),
      updatedAt: new Date('2025-01-04T16:45:00Z')
    });

    const msg6Content = "Thank you for the detailed information. Our inspection confirmed several of these issues. We have immediately addressed the blocked exits and are working on the other safety concerns. We may need to follow up with you for additional details as our investigation progresses.";
    const msg6Encrypted = encryptMessage(msg6Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60006'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20002'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00003'), // Michael
      content: msg6Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg6Encrypted.iv,
        contentTag: msg6Encrypted.tag
      },
      readBy: [],
      createdAt: new Date('2025-01-14T16:30:00Z'),
      updatedAt: new Date('2025-01-14T16:30:00Z')
    });

    // Messages for conversation 3 (WB-2025-0010)
    const msg7Content = "I understand this is a sensitive matter. We want to ensure you feel safe throughout this process. Can you provide more details about the incidents you witnessed? All information will be kept strictly confidential.";
    const msg7Encrypted = encryptMessage(msg7Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60007'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20003'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00002'), // Alexandra
      content: msg7Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg7Encrypted.iv,
        contentTag: msg7Encrypted.tag
      },
      readBy: [{
        userId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'),
        readAt: new Date('2024-12-29T11:00:00Z')
      }],
      createdAt: new Date('2024-12-29T10:30:00Z'),
      updatedAt: new Date('2024-12-29T10:30:00Z')
    });

    const msg8Content = "There have been multiple incidents of inappropriate comments and behavior. The manager has made several employees uncomfortable with personal questions and inappropriate jokes. I have witnessed at least three separate incidents in the past month.";
    const msg8Encrypted = encryptMessage(msg8Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60008'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20003'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00005'), // Jane
      content: msg8Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg8Encrypted.iv,
        contentTag: msg8Encrypted.tag
      },
      readBy: [{
        userId: new mongoose.Types.ObjectId('65f1234567890abcdef00002'),
        readAt: new Date('2025-01-02T14:20:00Z')
      }],
      createdAt: new Date('2025-01-02T13:45:00Z'),
      updatedAt: new Date('2025-01-02T13:45:00Z')
    });

    const msg9Content = "We have begun our investigation and have spoken with HR. We may need to schedule a formal interview with you to gather more details. Would you be comfortable with that? We can arrange for this to be done confidentially and outside of normal work hours if preferred.";
    const msg9Encrypted = encryptMessage(msg9Content);
    messages.push({
      _id: new mongoose.Types.ObjectId('65f1234567890abcdef60009'),
      conversationId: new mongoose.Types.ObjectId('65f1234567890abcdef20003'),
      senderId: new mongoose.Types.ObjectId('65f1234567890abcdef00002'), // Alexandra
      content: msg9Encrypted.encryptedContent,
      messageType: 'text',
      isEncrypted: true,
      encryptionData: {
        contentIv: msg9Encrypted.iv,
        contentTag: msg9Encrypted.tag
      },
      readBy: [],
      createdAt: new Date('2025-01-13T14:15:00Z'),
      updatedAt: new Date('2025-01-13T14:15:00Z')
    });

    await Message.insertMany(messages);
    console.log(`✅ Seeded ${messages.length} encrypted messages`);

    // Seed notifications
    console.log('🔔 Seeding notifications...');
    await Notification.insertMany(notifications);
    console.log(`✅ Seeded ${notifications.length} notifications`);

    // Seed pricing plans
    console.log('💰 Seeding pricing plans...');
    await PricingPlan.insertMany(seedPricingPlans);
    console.log(`✅ Seeded ${seedPricingPlans.length} pricing plans`);

    // Seed blog posts
    console.log('📝 Seeding blog posts...');
    await Blog.insertMany(seedBlogPosts);
    console.log(`✅ Seeded ${seedBlogPosts.length} blog posts`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Companies: ${companies.length}`);
    console.log(`   Users: ${users.length}`);
    console.log(`   Reports: ${reports.length}`);
    console.log(`   Conversations: ${conversations.length}`);
    console.log(`   Messages: ${messages.length}`);
    console.log(`   Notifications: ${notifications.length}`);
    console.log(`   Pricing Plans: ${seedPricingPlans.length}`);
    console.log(`   Blog Posts: ${seedBlogPosts.length}`);
    
    console.log('\n🔑 Test Credentials:');
    console.log('   Admin: <EMAIL> / password123');
    console.log('   Investigator 1: <EMAIL> / password123');
    console.log('   Investigator 2: <EMAIL> / password123');
    console.log('   Whistleblower 1: <EMAIL> / password123');
    console.log('   Whistleblower 2: <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the seeding function
// Check if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding process failed:', error);
      process.exit(1);
    });
}

export default seedDatabase;