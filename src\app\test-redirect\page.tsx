"use client";

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function TestRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Test redirect after 2 seconds
    const timer = setTimeout(() => {
      console.log('Redirecting to dashboard...');
      router.push('/dashboard/whistleblower');
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Testing Redirect</h1>
        <p>Redirecting to dashboard in 2 seconds...</p>
      </div>
    </div>
  );
}