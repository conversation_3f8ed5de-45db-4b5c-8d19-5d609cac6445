"use client";

import Image from "next/image";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { ChevronDown, Bell, Settings, Menu, X, LogOut } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { memo, useState, useEffect, useCallback } from "react";
import { NavItem, Notification, ListItemProps } from "@/lib/types";
import { languageOptions, PROFILE_ITEMS, NAVIGATION_ITEMS, ADMIN_NAVIGATION_ITEMS } from "@/lib/mockData";
import { getNotificationIcon, getPriorityColor, formatTimestamp } from "@/lib/utils";
import { useMessageIndicators } from "@/hooks/useMessageIndicators";
import { useNotifications } from "@/hooks/useNotifications";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useConnectionStatus } from "@/providers/RealTimeProvider";
import { ConnectionStatusIndicator } from "@/components/ui/connection-status";


const ListItem = memo(({ href, title, children, onClick }: ListItemProps) => (
    <li>
        <Link
            href={href}
            className="block rounded-md px-3 py-2 hover:bg-green-100 transition-colors duration-300 ease-in-out"
            onClick={onClick}
        >
            <div className="font-semibold text-[#1E4841] group-hover:text-[#2A5D54]">{title}</div>
            {children && <div className="text-sm text-[#1E4841] group-hover:text-[#2A5D54]">{children}</div>}
        </Link>
    </li>
));

ListItem.displayName = 'ListItem';

const DropdownContent = memo(({ items, onItemClick }: { items: NavItem[], onItemClick?: () => void }) => (
    <ul className="grid gap-2 p-2">
        {items.map(({ href, title, description }) => (
            <ListItem key={href} href={href} title={title} onClick={onItemClick}>
                {description}
            </ListItem>
        ))}
    </ul>
));

DropdownContent.displayName = 'DropdownContent';

// Notification Item Component
const NotificationItem = memo(({ notification, onMarkAsRead }: {
    notification: Notification;
    onMarkAsRead: (id: string) => void;
}) => {
    const Icon = getNotificationIcon(notification.type);
    const priorityColor = getPriorityColor(notification.priority);

    const handleClick = useCallback(() => {
        const notificationId = notification._id;
        if (notification.status === 'unread' && notificationId) {
            onMarkAsRead(notificationId);
        }
    }, [notification, onMarkAsRead]);

    return (
        <div
            className={`p-3 hover:bg-gray-50 transition-colors cursor-pointer border-l-4 ${notification.status === 'unread'
                ? 'border-l-green-500 bg-green-50/30'
                : 'border-l-transparent'
                }`}
            onClick={handleClick}
        >
            <div className="flex items-start gap-3">
                <div className={`p-1.5 rounded-full ${notification.status === 'unread' ? 'bg-green-100' : 'bg-gray-100'
                    }`}>
                    <Icon className={`w-4 h-4 ${priorityColor}`} />
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <p className={`text-sm font-medium text-gray-900 ${notification.status === 'unread' ? 'font-semibold' : ''
                            }`}>
                            {notification.title}
                        </p>
                        <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                            {notification.createdAt ? formatTimestamp(notification.createdAt.toString()) : 'N/A'}
                        </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {notification.message}
                    </p>
                    {notification.status === 'unread' && (
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    )}
                </div>
            </div>
        </div>
    );
});

NotificationItem.displayName = 'NotificationItem';

// Mobile Navigation Item Component
const MobileNavItem = memo(({ item, isActive, onClick }: {
    item: typeof NAVIGATION_ITEMS[0];
    isActive: boolean;
    onClick: () => void;
}) => {
    const Icon = item.icon;
    const messageIndicator = useMessageIndicators();
    const isSecureMessage = item.url === '/dashboard/whistleblower/secure-message';

    return (
        <Link
            href={item.url}
            onClick={onClick}
            className={`flex items-center gap-3 px-4 py-3 transition-colors duration-300 ${
                isActive
                    ? 'bg-[#BBF49C] text-[#1E4841] font-semibold'
                    : 'text-[#6B7271] hover:bg-[#BBF49C] hover:text-[#1E4841]'
            }`}
        >
            <div className="relative">
                <Icon className="w-5 h-5" />
                {isSecureMessage && messageIndicator.hasUnread && (
                    <div className="absolute -top-1 -right-1 bg-[#EF4444] text-white rounded-full flex items-center justify-center font-medium w-4 h-4 text-xs">
                        {messageIndicator.formattedCount}
                    </div>
                )}
            </div>
            <span className="font-medium">{item.title}</span>
        </Link>
    );
});

MobileNavItem.displayName = 'MobileNavItem';

const Header = memo(function Header() {
    const { user, logout } = useAuth();
    const [showNotifications, setShowNotifications] = useState(false);
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState(languageOptions[0]);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const pathname = usePathname();

    // Use real-time context
    const { isConnected } = useConnectionStatus();
    
    // Use real notification hook
    const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();

    // Accessibility: close popover with Escape
    useEffect(() => {
        if (!showNotifications) return;
        const handler = (e: KeyboardEvent) => {
            if (e.key === "Escape") setShowNotifications(false);
        };
        window.addEventListener("keydown", handler);
        return () => window.removeEventListener("keydown", handler);
    }, [showNotifications]);

    const handleMarkAsRead = (notificationId: string) => {
        markAsRead(notificationId);
    };

    const handleMarkAllAsRead = () => {
        markAllAsRead();
    };

    const handleLanguageChange = (languageCode: string) => {
        const language = languageOptions.find(lang => lang.code === languageCode);
        if (language) {
            setSelectedLanguage(language);
            // Language change logic
            console.log('Language changed to:', language);
        }
    };

    // Mobile menu handlers
    const toggleMobileMenu = useCallback(() => {
        setIsMobileMenuOpen(prev => !prev);
    }, []);

    const closeMobileMenu = useCallback(() => {
        setIsMobileMenuOpen(false);
    }, []);

    // Prevent body scroll when mobile menu is open
    useEffect(() => {
        if (isMobileMenuOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isMobileMenuOpen]);
    
    // Handle logout
    const handleLogout = async () => {
        try {
            setIsLoggingOut(true);
            logout(); // This already handles the redirect
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            setIsLoggingOut(false);
            closeMobileMenu();
        }
    };
    
    return (
        <>
            <header className="w-full h-14 sm:h-16 flex justify-between items-center bg-white border-b-2 p-3 sm:p-4 md:p-6 relative z-50">
                <div className="flex items-center gap-1 sm:gap-2">
                    <Image
                        src="/dashboard/header/icon.svg"
                        alt="7IRIS logo"
                        width={32}
                        height={33}
                        className="w-6 h-auto sm:w-8"
                        priority
                    />
                    <p className="text-sm sm:text-base font-semibold text-[#1F2937] hidden sm:block">7IRIS Whistleblower Platform</p>
                    <p className="text-sm font-semibold text-[#1F2937] sm:hidden">7IRIS</p>
                </div>
                
                <div className="flex justify-between items-center gap-2 sm:gap-4 md:gap-8">
                    {/* Connection Status Indicator - Hidden on mobile */}
                    <div className="hidden md:block">
                        <ConnectionStatusIndicator 
                            connectionStatus={{
                                isConnected,
                                isReconnecting: false,
                                reconnectAttempts: 0,
                                latency: 0,
                                lastConnected: new Date()
                            }}
                            onReconnect={() => {}}
                            className="text-xs"
                        />
                    </div>

                    {/* Enhanced Language Select - Hidden on lg and below, shown on xl+ */}
                    <div className="hidden xl:block">
                        <Select
                            name="language"
                            aria-label="Select language"
                            value={selectedLanguage.code}
                            onValueChange={handleLanguageChange}
                        >
                            <SelectTrigger className="w-fit p-1 sm:p-2 text-[#4B5563] text-xs sm:text-sm font-normal border-none shadow-none hover:bg-[#ECF4E9] transition-colors">
                                <div className="flex items-center gap-1 sm:gap-2">
                                    <SelectValue />
                                </div>
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    {languageOptions.map((language) => (
                                        <SelectItem key={language.code} value={language.code} className="hover:bg-[#ECF4E9] transition-colors">
                                            <div className="flex items-center gap-2">
                                                <Image
                                                    src={`/dashboard/header/flags/${language.code}.svg`}
                                                    alt={language.name}
                                                    width={20}
                                                    height={15}
                                                    className="rounded-sm h-auto w-4 sm:w-6"
                                                />
                                                <span className="text-xs sm:text-sm">{language.name}</span>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Notifications Popover - Hidden on lg and below, shown on xl+ */}
                    <div className="hidden xl:block">
                        <Popover open={showNotifications} onOpenChange={setShowNotifications}>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="relative p-1 sm:p-2 hover:bg-gray-100 rounded-full transition-colors"
                                    aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
                                >
                                    {unreadCount > 0 ? (
                                        <Image
                                            src="/dashboard/header/bell-icon.svg"
                                            alt="bell icon with notification"
                                            width={20}
                                            height={20}
                                            priority
                                            className="h-6 w-auto sm:h-8 mt-5"
                                        />
                                    ) : (
                                        <Bell className="w-6 h-6 sm:w-8 sm:h-8 text-[#4B5563]" />
                                    )}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent
                                align="end"
                                className="w-72 sm:w-80 p-0 bg-white rounded-lg shadow-lg border"
                                sideOffset={5}
                            >
                                <div className="p-4 border-b">
                                    <div className="flex items-center justify-between">
                                        <h3 className="font-semibold text-gray-900">Notifications</h3>
                                        {unreadCount > 0 && (
                                            <Button
                                                aria-label="Mark all as read"
                                                variant="ghost"
                                                size="sm"
                                                onClick={handleMarkAllAsRead}
                                                className="text-xs text-green-600 hover:text-green-700 hover:bg-green-50"
                                            >
                                                Mark all as read
                                            </Button>
                                        )}
                                    </div>
                                    {unreadCount > 0 && (
                                        <p className="text-sm text-gray-500 mt-1">
                                            You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                                        </p>
                                    )}
                                </div>

                                <div className="max-h-96 overflow-y-auto">
                                    {notifications.length > 0 ? (
                                        <>
                                            {notifications.slice(0, 5).map((notification, index) => (
                                                <NotificationItem
                                                    key={notification._id || `notification-${index}`}
                                                    notification={notification}
                                                    onMarkAsRead={handleMarkAsRead}
                                                />
                                            ))}
                                            {notifications.length > 5 && (
                                                <div className="p-3 border-t">
                                                    <Link
                                                        href="/dashboard/notifications"
                                                        className="text-sm text-green-600 hover:text-green-700 font-medium"
                                                    >
                                                        View all notifications ({notifications.length})
                                                    </Link>
                                                </div>
                                            )}
                                        </>
                                    ) : (
                                        <div className="p-8 text-center">
                                            <Bell className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                                            <p className="text-sm text-gray-500">No notifications yet</p>
                                        </div>
                                    )}
                                </div>
                            </PopoverContent>
                        </Popover>
                    </div>

                    {/* User Menu - Hidden on lg and below, shown on xl+ */}
                    <div className="hidden xl:flex items-center">
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    aria-label="Open user menu"
                                    variant="ghost"
                                    className="text-[#374151] text-xs sm:text-sm font-normal hover:text-gray-900 hover:bg-transparent py-3 sm:py-5 transition-colors duration-300 group">
                                    <Avatar className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8">
                                        <AvatarImage src="/dashboard/header/user.svg" alt="User profile" />
                                        <AvatarFallback>{user?.name?.[0] || 'U'}</AvatarFallback>
                                    </Avatar>
                                    <span className="hidden sm:inline">{user?.name || 'User'}</span>
                                    <span className="sm:hidden">{user?.name?.split(' ')[0] || 'User'}</span>
                                    <ChevronDown className="ml-1 transition-transform duration-300 group-data-[state=open]:rotate-180" size={14} />
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent sideOffset={5} align="center" className="w-[120px] sm:w-[150px] bg-[#ECF4E9] rounded-md shadow-lg p-0">
                                <ul className="grid gap-2 p-2">
                                    {PROFILE_ITEMS.map(({ href, title }) => (
                                        <li key={href}>
                                            {title === "Logout" ? (
                                                <button
                                                    onClick={handleLogout}
                                                    className="block rounded-md px-3 py-2 hover:bg-green-100 transition-colors duration-300 ease-in-out w-full text-left"
                                                    disabled={isLoggingOut}
                                                >
                                                    <div className="font-semibold text-[#1E4841] group-hover:text-[#2A5D54]">
                                                        {isLoggingOut ? "Logging out..." : title}
                                                    </div>
                                                </button>
                                            ) : (
                                                <Link
                                                    href={href}
                                                    className="block rounded-md px-3 py-2 hover:bg-green-100 transition-colors duration-300 ease-in-out"
                                                >
                                                    <div className="font-semibold text-[#1E4841] group-hover:text-[#2A5D54]">{title}</div>
                                                </Link>
                                            )}
                                        </li>
                                    ))}
                                </ul>
                            </PopoverContent>
                        </Popover>
                    </div>

                    {/* Hamburger Menu Button - visible on lg and below, positioned on right */}
                    <Button
                        variant="ghost"
                        size="sm"
                        className="lg:hidden p-1 sm:p-2 hover:bg-[#ECF4E9] transition-colors duration-300"
                        onClick={toggleMobileMenu}
                        aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
                        aria-expanded={isMobileMenuOpen}
                    >
                        {isMobileMenuOpen ? (
                            <X className="h-5 w-5 sm:h-6 sm:w-6 text-[#1E4841] transition-transform duration-300" />
                        ) : (
                            <Menu className="h-5 w-5 sm:h-6 sm:w-6 text-[#1E4841] transition-transform duration-300" />
                        )}
                    </Button>
                </div>
            </header>

            {/* Mobile Navigation Menu */}
            {isMobileMenuOpen && (
                <div className="fixed inset-0 z-40 lg:hidden">
                    {/* Backdrop */}
                    <div
                        className="fixed inset-0 bg-black bg-opacity-50 md:opacity-5 transition-opacity duration-300"
                        onClick={closeMobileMenu}
                    />

                    {/* Mobile Menu - Full width on mobile, right-side on md */}
                    <div className="fixed top-14 sm:top-16 left-0 right-0 md:left-auto md:right-0 md:w-80 bg-[#ECF4E9] shadow-lg max-h-[calc(100vh-3.5rem)] sm:max-h-[calc(100vh-4rem)] overflow-y-auto animate-in slide-in-from-top-2 md:slide-in-from-right-2 duration-300">
                        <div className="py-2">
                            {/* Navigation Items - Only show on devices smaller than md */}
                            <div className="md:hidden border-b border-green-200 pb-2">
                                {NAVIGATION_ITEMS.map((item) => {
                                    const isActive = pathname === item.url;
                                    return (
                                        <MobileNavItem
                                            key={item.url}
                                            item={item}
                                            isActive={isActive}
                                            onClick={closeMobileMenu}
                                        />
                                    );
                                })}
                            </div>

                            {/* Header Components Section */}
                            <div className="border-b border-green-200 py-2">
                                <div className="px-4 py-2">
                                    <h3 className="text-sm font-semibold text-[#1E4841] mb-3">Settings & Notifications</h3>

                                    {/* Language Selector */}
                                    <div className="mb-4">
                                        <Label className="text-xs font-medium text-[#6B7271] mb-2 block">Language</Label>
                                        <Select
                                            name="mobile-language"
                                            aria-label="Select language"
                                            value={selectedLanguage.code}
                                            onValueChange={handleLanguageChange}
                                        >
                                            <SelectTrigger className="w-full p-3 text-[#4B5563] text-sm font-normal border border-green-200 shadow-none hover:bg-[#BBF49C] transition-colors">
                                                <div className="flex items-center gap-2">
                                                    <SelectValue />
                                                </div>
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    {languageOptions.map((language) => (
                                                        <SelectItem key={language.code} value={language.code} className="hover:bg-[#ECF4E9] transition-colors">
                                                            <div className="flex items-center gap-2">
                                                                <Image
                                                                    src={`/dashboard/header/flags/${language.code}.svg`}
                                                                    alt={language.name}
                                                                    width={20}
                                                                    height={15}
                                                                    className="rounded-sm h-3 w-auto"
                                                                />
                                                                <span className="text-sm">{language.name}</span>
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Notifications */}
                                    <div className="mb-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <Label className="text-xs font-medium text-[#6B7271]">Notifications</Label>
                                            {unreadCount > 0 && (
                                                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                                    {unreadCount}
                                                </span>
                                            )}
                                        </div>
                                        <Link
                                            href="/dashboard/whistleblower/notifications"
                                            onClick={closeMobileMenu}
                                            className="flex items-center gap-3 p-3 border border-green-200 rounded-md hover:bg-[#BBF49C] transition-colors duration-300"
                                        >
                                            <Bell className="w-5 h-5 text-[#6B7271]" />
                                            <span className="text-sm font-medium text-[#6B7271]">View All Notifications</span>
                                        </Link>
                                    </div>

                                    {/* User Profile */}
                                    <div>
                                        <Label className="text-xs font-medium text-[#6B7271] mb-2 block">Profile</Label>
                                        <div className="flex items-center gap-3 p-3 border border-green-200 rounded-md bg-white">
                                            <Avatar className="h-8 w-8">
                                                <AvatarImage src="/dashboard/header/user.svg" alt="User profile" />
                                                <AvatarFallback>{user?.name?.[0] || 'U'}</AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <p className="text-sm font-medium text-[#1E4841]">{user?.name}</p>
                                                <p className="text-xs text-[#6B7271]">{user?.role}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Profile Actions - Only show on devices smaller than md */}
                            <div className="md:hidden py-2">
                                {PROFILE_ITEMS.map((item) => (
                                    item.title === "Logout" ? (
                                        <button
                                            key={item.href}
                                            onClick={handleLogout}
                                            className="flex items-center gap-3 px-4 py-3 text-[#6B7271] hover:bg-[#BBF49C] hover:text-[#1E4841] transition-colors duration-300 w-full text-left"
                                            disabled={isLoggingOut}
                                        >
                                            <LogOut className="w-5 h-5" />
                                            <span className="font-medium">{isLoggingOut ? "Logging out..." : item.title}</span>
                                        </button>
                                    ) : (
                                        <Link
                                            key={item.href}
                                            href={item.href}
                                            onClick={closeMobileMenu}
                                            className="flex items-center gap-3 px-4 py-3 text-[#6B7271] hover:bg-[#BBF49C] hover:text-[#1E4841] transition-colors duration-300"
                                        >
                                            {item.title === "Change Profile" && <Settings className="w-5 h-5" />}
                                            {item.title === "Settings" && <Settings className="w-5 h-5" />}
                                            <span className="font-medium">{item.title}</span>
                                        </Link>
                                    )
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
});

export default Header;
