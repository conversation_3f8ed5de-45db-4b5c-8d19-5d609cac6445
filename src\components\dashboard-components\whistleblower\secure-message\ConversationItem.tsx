"use client";

import { LockKeyhole } from "lucide-react";
import { ConversationData } from "@/lib/types";

interface ConversationItemProps {
  conversation: ConversationData;
  isActive: boolean;
  onClick: () => void;
}

export default function ConversationItem({ conversation, isActive, onClick }: ConversationItemProps) {
  const baseClasses = "flex flex-col justify-between group cursor-pointer transition-all duration-200 p-3 border-b border-gray-100 hover:bg-gray-50";
  const activeClasses = isActive ? "bg-[#ECF4E9] border-l-4 border-l-[#1E4841]" : "bg-white";
  const unreadClasses = conversation.isUnread ? "font-medium" : "";
  const classNames = `${baseClasses} ${activeClasses} ${unreadClasses}`.trim();
  const avatarBg = conversation.avatarBg || "bg-[#BBF49C]";

  return (
    <div
      className={classNames}
      onClick={onClick}
      role="button"
      tabIndex={0}
      aria-label={`Conversation with ${conversation.name} about ${conversation.caseId}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
    >
      <div className="flex items-center gap-3 mb-2">
        <div className="relative">
          <div className={`h-10 w-10 rounded-full flex items-center justify-center ${avatarBg}`}>
            <span className="text-sm font-medium text-[#1E4841]">
              {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
            </span>
          </div>
          {conversation.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium text-[#111827] truncate">
                {conversation.name}
              </p>
              {conversation.isOnline && (
                <span className="text-xs text-green-600 font-medium">Online</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <p className="text-xs text-[#6B7280]">{conversation.time}</p>
              {conversation.isUnread && (
                <div className="w-2.5 h-2.5 bg-[#EF4444] rounded-full"></div>
              )}
            </div>
          </div>
          <p className="text-xs text-[#6B7280] mb-1">{conversation.caseId}</p>
        </div>
      </div>
      <div className="flex items-center justify-between">
        <p className="text-sm text-[#4B5563] truncate flex-1 mr-2">
          {conversation.isTyping ? (
            <span className="text-green-600 italic">Typing...</span>
          ) : (
            conversation.lastMessage
          )}
        </p>
        <div className="flex items-center gap-1">
          <LockKeyhole className="w-3 h-3 text-[#1E4841]" />
        </div>
      </div>
    </div>
  );
}