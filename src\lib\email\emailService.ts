import nodemailer from 'nodemailer';

interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject: string;
  message: string;
  department?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_SERVER_HOST || process.env.SMTP_HOST,
      port: parseInt(process.env.EMAIL_SERVER_PORT || process.env.SMTP_PORT || '587'),
      secure: process.env.EMAIL_SERVER_SECURE === 'true' || process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_SERVER_USER || process.env.SMTP_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD || process.env.SMTP_PASS,
      },
      tls: {
        rejectUnauthorized: false // For development/testing
      }
    });
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Whistleblower System'}" <${process.env.EMAIL_FROM || process.env.SMTP_FROM_EMAIL || process.env.EMAIL_SERVER_USER || process.env.SMTP_USER}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      };

      const info = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', info.messageId);
      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  async sendContactFormEmail(formData: ContactFormData): Promise<boolean> {
    try {
      const recipientEmail = process.env.CONTACT_FORM_RECIPIENT || process.env.EMAIL_SERVER_USER || process.env.SMTP_USER;
      
      if (!recipientEmail) {
        console.error('No recipient email configured for contact form');
        return false;
      }

      const departmentMap = {
        sales: 'Sales Team',
        technical: 'Technical Support',
        customer: 'Customer Service',
        media: 'Media Relations'
      };

      const departmentName = formData.department 
        ? departmentMap[formData.department as keyof typeof departmentMap] || 'General Inquiry'
        : 'General Inquiry';

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">New Contact Form Submission</h1>
              <p style="color: #6B7280; margin: 5px 0 0 0; font-size: 14px;">Department: ${departmentName}</p>
            </div>
            
            <div style="margin-bottom: 25px;">
              <h2 style="color: #1E4841; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #BBF49C; padding-left: 15px;">Contact Information</h2>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151; width: 120px;">Name:</td>
                  <td style="padding: 8px 0; color: #6B7280;">${formData.name}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Email:</td>
                  <td style="padding: 8px 0; color: #6B7280;"><a href="mailto:${formData.email}" style="color: #1E4841; text-decoration: none;">${formData.email}</a></td>
                </tr>
                ${formData.phone ? `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Phone:</td>
                  <td style="padding: 8px 0; color: #6B7280;"><a href="tel:${formData.phone}" style="color: #1E4841; text-decoration: none;">${formData.phone}</a></td>
                </tr>
                ` : ''}
                ${formData.company ? `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Company:</td>
                  <td style="padding: 8px 0; color: #6B7280;">${formData.company}</td>
                </tr>
                ` : ''}
              </table>
            </div>
            
            <div style="margin-bottom: 25px;">
              <h2 style="color: #1E4841; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #BBF49C; padding-left: 15px;">Subject</h2>
              <p style="color: #374151; font-size: 16px; margin: 0; font-weight: 500;">${formData.subject}</p>
            </div>
            
            <div style="margin-bottom: 25px;">
              <h2 style="color: #1E4841; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #BBF49C; padding-left: 15px;">Message</h2>
              <div style="background-color: #F9FAFB; padding: 20px; border-radius: 6px; border-left: 4px solid #BBF49C;">
                <p style="color: #374151; line-height: 1.6; margin: 0; white-space: pre-wrap;">${formData.message}</p>
              </div>
            </div>
            
            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                This message was sent via the Whistleblower System contact form.<br>
                Submitted on ${new Date().toLocaleString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric', 
                  hour: 'numeric', 
                  minute: '2-digit',
                  timeZoneName: 'short'
                })}
              </p>
            </div>
          </div>
        </div>
      `;

      const textContent = `
New Contact Form Submission - ${departmentName}

Contact Information:
Name: ${formData.name}
Email: ${formData.email}
${formData.phone ? `Phone: ${formData.phone}` : ''}
${formData.company ? `Company: ${formData.company}` : ''}

Subject: ${formData.subject}

Message:
${formData.message}

---
This message was sent via the Whistleblower System contact form.
Submitted on ${new Date().toLocaleString()}
      `;

      return await this.sendEmail({
        to: recipientEmail,
        subject: `Contact Form: ${formData.subject} - ${departmentName}`,
        text: textContent,
        html: htmlContent
      });
    } catch (error) {
      console.error('Error sending contact form email:', error);
      return false;
    }
  }

  async sendAutoReplyEmail(formData: ContactFormData): Promise<boolean> {
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="border-bottom: 3px solid #1E4841; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
              <h1 style="color: #1E4841; margin: 0; font-size: 24px;">Thank You for Contacting Us</h1>
            </div>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">Dear ${formData.name},</p>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              Thank you for reaching out to us through our contact form. We have received your message regarding 
              "<strong>${formData.subject}</strong>" and appreciate you taking the time to contact us.
            </p>
            
            <div style="background-color: #F0F9FF; padding: 20px; border-radius: 6px; border-left: 4px solid #BBF49C; margin: 25px 0;">
              <h3 style="color: #1E4841; margin: 0 0 10px 0; font-size: 16px;">What happens next?</h3>
              <ul style="color: #374151; margin: 0; padding-left: 20px;">
                <li style="margin-bottom: 8px;">Our team will review your message within 24-48 hours</li>
                <li style="margin-bottom: 8px;">You'll receive a personalized response from the appropriate department</li>
                <li style="margin-bottom: 8px;">For urgent matters, please call our support line</li>
              </ul>
            </div>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              If you have any additional questions or need immediate assistance, please don't hesitate to reach out to us.
            </p>
            
            <div style="border-top: 1px solid #E5E7EB; padding-top: 20px; text-align: center;">
              <p style="color: #1E4841; font-weight: bold; margin: 0 0 5px 0;">Whistleblower System Team</p>
              <p style="color: #6B7280; font-size: 12px; margin: 0;">
                This is an automated response. Please do not reply to this email.
              </p>
            </div>
          </div>
        </div>
      `;

      const textContent = `
Thank You for Contacting Us

Dear ${formData.name},

Thank you for reaching out to us through our contact form. We have received your message regarding "${formData.subject}" and appreciate you taking the time to contact us.

What happens next?
- Our team will review your message within 24-48 hours
- You'll receive a personalized response from the appropriate department
- For urgent matters, please call our support line

If you have any additional questions or need immediate assistance, please don't hesitate to reach out to us.

Best regards,
Whistleblower System Team

---
This is an automated response. Please do not reply to this email.
      `;

      return await this.sendEmail({
        to: formData.email,
        subject: `Thank you for contacting us - ${formData.subject}`,
        text: textContent,
        html: htmlContent
      });
    } catch (error) {
      console.error('Error sending auto-reply email:', error);
      return false;
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('SMTP connection verified successfully');
      return true;
    } catch (error) {
      console.error('SMTP connection failed:', error);
      return false;
    }
  }
}

export const emailService = new EmailService();
export default EmailService;