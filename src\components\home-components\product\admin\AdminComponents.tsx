"use client";
import React, { useState, useRef, useCallback, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { 
  ResponsibilityCard,
  UserCard,
  CaseCard,
  InsightCard,
  AdminSecurityCard,
  AdminTestimonialCard
} from "../shared/AdminCards";
import { 
  adminResponsibilities,
  adminUsers,
  adminCases,
  adminInsights,
  adminSecurities,
  adminTestimonials
} from "@/lib/mockData/productsData";

// Hero Section Component
export const AdminHero: React.FC = () => (
  <section className="mx-auto px-4 py-12 sm:px-8 sm:py-16 md:px-12 md:py-20 lg:px-24 lg:py-28 xl:px-[170px] xl:py-[100px] bg-gradient-to-r from-[#1E4841]/95 from-90% via-[#1E4841]/90 via-98% to-[#1E4841]/85 relative">
    <Image
      src="/desktop/products/admin/hero.jpg"
      alt="Whistleblower background"
      fill
      className="object-cover mix-blend-luminosity opacity-20"
      priority
    />
    <div className="relative z-10 text-center md:text-left">
      <p className="ml-2 sm:ml-4 text-2xl leading-tight sm:text-3xl sm:leading-tight md:text-4xl/14 xl:text-5xl/16 font-bold text-white mb-4 sm:mb-5 md:mb-6 w-full xl:w-2/3">Total Oversight.<span className="md:hidden"><br /></span> Seamless Control.</p>
      <p className="ml-2 sm:ml-4 text-base sm:text-lg md:text-xl xl:text-xl text-white mb-6 sm:mb-7 md:mb-8 w-full sm:w-4/5 md:w-3/4 xl:w-3/5">Manage whistleblowing operations across teams with precision and confidence. Centralize your compliance workflow with our powerful Admin Portal.</p>
      <Button
        variant="default"
        className="ml-2 sm:ml-4 px-3 py-2 sm:px-4 sm:py-3 md:px-6 md:py-4 lg:px-9 lg:py-7 text-sm sm:text-base md:text-lg xl:text-xl text-[#1E4841] bg-[#BBF49C] border border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
        aria-label="View Investigator Dashboard Demo"
      >
        <div className="flex items-center gap-1 sm:gap-2">
          <p className="hidden sm:block text-[#1E4841]">View Admin Console Demo </p>
          <p className="block sm:hidden text-[#1E4841]">View Demo</p>
          <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" />
        </div>
      </Button>
      <Image
        src="/desktop/products/admin/placeholder.svg"
        alt="Whistleblower background"
        width={1208}
        height={753}
        className="w-full h-auto relative mx-auto mt-12 sm:mt-16 md:mt-20 rounded-lg border"
        priority
      />
    </div>
  </section>
);

// Responsibilities Section Component
export const AdminResponsibilities: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-2 text-center">Core Admin Responsibilities</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-16 xl:px-55 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563] mb-6 text-center">Our Admin Portal empowers your compliance team with comprehensive tools to manage every aspect of your whistleblowing program.</p>
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 md:gap-12 lg:gap-2 xl:gap-8 w-full px-2 sm:px-4 md:px-8 lg:px-12 xl:px-15 mt-8">
      {adminResponsibilities.map((responsibility, index) => (
        <ResponsibilityCard key={index} {...responsibility} />
      ))}
    </div>
  </section>
);

// Dashboard Section Component
export const AdminDashboard: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <div className="flex flex-col lg:flex-row-reverse justify-between items-center gap-4 sm:gap-6 lg:gap-8 xl:gap-4.5 w-full">
      <Image
        src="/desktop/products/admin/dashboardgraph.svg"
        alt="Powerful Admin Dashboard"
        width={624}
        height={443}
        className="h-auto w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-md xl:max-w-4xl lg:h-120"
      />
      <div className="grid grid-rows-1 w-full lg:mt-10 xl:mt-12 px-2 sm:px-4 lg:px-4 xl:px-4">
        <div className="flex flex-col gap-4 sm:gap-6 mt-4 sm:mt-0 lg:-mt-8">
          <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841]">Powerful Admin Dashboard</p>
          <p className="text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563]">Get a comprehensive view of your whistleblowing program with our intuitive admin dashboard. Monitor case status, team performance, and compliance metrics at a glance.</p>
        </div>
        {adminInsights.map((insight, index) => (
          <InsightCard key={index} {...insight} />
        ))}
      </div>
    </div>
  </section>
);

// User Management Section Component
export const AdminUserManagement: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <div className="flex flex-col lg:flex-row justify-between items-center gap-4 sm:gap-6 lg:gap-8 xl:gap-8 w-full">
      <Image
        src="/desktop/products/admin/usermanagement.svg"
        alt="User & Role Management"
        width={624}
        height={443}
        className="h-auto w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-md xl:max-w-4xl lg:h-120"
      />
      <div className="grid grid-rows-1 w-full lg:mt-10 xl:mt-12 px-2 sm:px-4 lg:px-4 xl:px-4">
        <div className="flex flex-col gap-4 sm:gap-6 mt-4 sm:mt-0 lg:-mt-8">
          <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841]">User & Role Management</p>
          <p className="text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563]">Define roles, assign permissions, and manage access levels for all team members with granular control.</p>
        </div>
        {adminUsers.map((user, index) => (
          <UserCard key={index} {...user} />
        ))}
      </div>
    </div>
  </section>
);

// Case Assignment Section Component
export const AdminCaseAssignment: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <div className="flex flex-col lg:flex-row-reverse justify-between items-center gap-4 sm:gap-6 lg:gap-8 xl:gap-8 w-full">
      <Image
        src="/desktop/products/admin/caseassignment.svg"
        alt="Smart Case Assignment"
        width={624}
        height={442}
        className="h-auto w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-md xl:max-w-4xl lg:h-120"
      />
      <div className="grid grid-rows-1 w-full lg:mt-10 xl:mt-12 px-2 sm:px-4 lg:px-4 xl:px-4">
        <div className="flex flex-col gap-4 sm:gap-6 mt-4 sm:mt-0 lg:-mt-8">
          <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841]">Smart Case Assignment</p>
          <p className="text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563]">Efficiently assign cases to the right investigators based on expertise, department, or case type—all with full manual control.</p>
        </div>
        {adminCases.map((caseItem, index) => (
          <CaseCard key={index} {...caseItem} />
        ))}
      </div>
    </div>
  </section>
);

// Features Section Component
export const AdminFeatures: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-2 text-center">Comprehensive Admin Features</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-16 xl:px-55 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563] mb-6 text-center">Our Admin Portal provides all the tools you need to manage your whistleblowing program effectively.</p>
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 md:gap-6 xl:gap-8 mt-8 w-full">
      <div className="shadow-md hover:shadow-lg rounded-lg p-4 sm:p-6 transition-all duration-300 group">
        <div className="flex items-start justify-between mb-4 sm:mb-6 h-[140px] sm:h-[160px] md:h-[179px]">
          <Image
            src="/desktop/products/admin/auditexports.svg"
            alt="Exportable Audit Reports"
            width={316}
            height={179}
            className="rounded-lg w-auto h-full object-cover"
          />
        </div>
        <p className="font-bold text-lg sm:text-xl text-[#242E2C] mb-3 sm:mb-4">Exportable Audit Reports</p>
        <p className="text-[#1E4841] text-sm sm:text-base mb-4 sm:mb-6 leading-relaxed">
          Generate comprehensive audit reports with time-stamped activities for compliance and oversight. Export logs in multiple formats including PDF, CSV, and Excel for regulatory reporting.
        </p>
        <Link
          href="/products/admin"
          className="inline-flex items-center text-[#1E4841] font-semibold hover:text-[#2A5D54] transition-all duration-300 group-hover:translate-x-1"
        >
          Learn more <ArrowRight className="ml-2 h-3 w-3 sm:h-4 sm:w-4" />
        </Link>
      </div>
      <div className="shadow-md hover:shadow-lg rounded-lg p-4 sm:p-6 transition-all duration-300 group">
        <div className="flex items-start justify-between mb-4 sm:mb-6 h-[140px] sm:h-[160px] md:h-[179px]">
          <Image
            src="/desktop/products/admin/alertsystem.svg"
            alt="Notification & Alert Settings"
            width={316}
            height={179}
            className="rounded-lg w-auto h-full object-cover"
          />
        </div>
        <p className="font-bold text-lg sm:text-xl text-[#242E2C] mb-3 sm:mb-4">Notification & Alert Settings</p>
        <p className="text-[#1E4841] text-sm sm:text-base mb-4 sm:mb-6 leading-relaxed">
          Configure custom notification rules for different events and user roles. Set up escalation alerts for high-priority cases and approaching deadlines to ensure timely responses.
        </p>
        <Link
          href="/products/admin"
          className="inline-flex items-center text-[#1E4841] font-semibold hover:text-[#2A5D54] transition-all duration-300 group-hover:translate-x-1"
        >
          Learn more <ArrowRight className="ml-2 h-3 w-3 sm:h-4 sm:w-4" />
        </Link>
      </div>
      <div className="shadow-md hover:shadow-lg rounded-lg p-4 sm:p-6 transition-all duration-300 group md:col-span-2 xl:col-span-1">
        <div className="flex items-start justify-between mb-4 sm:mb-6 h-[140px] sm:h-[160px] md:h-[179px]">
          <Image
            src="/desktop/products/admin/platformsettings.svg"
            alt="Configurable Platform Settings"
            width={316}
            height={179}
            className="rounded-lg w-auto h-full object-cover"
          />
        </div>
        <p className="font-bold text-lg sm:text-xl text-[#242E2C] mb-3 sm:mb-4">Configurable Platform Settings</p>
        <p className="text-[#1E4841] text-sm sm:text-base mb-4 sm:mb-6 leading-relaxed">
          Customize your platform with configurable settings for identity policy (anonymous or identified), language preferences, and legal disclaimers to meet your organization&apos;s specific requirements.
        </p>
        <Link
          href="/products/admin"
          className="inline-flex items-center text-[#1E4841] font-semibold hover:text-[#2A5D54] transition-all duration-300 group-hover:translate-x-1"
        >
          Learn more <ArrowRight className="ml-2 h-3 w-3 sm:h-4 sm:w-4" />
        </Link>
      </div>
    </div>
  </section>
);

// Security Section Component
export const AdminSecurity: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center bg-[#1E4841]">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-white mb-2 text-center">Security & Governance</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-20 xl:px-60 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#D1D5DB] mb-10 sm:mb-12 md:mb-14 text-center">Our Admin Portal is built with enterprise-grade security and governance features to protect sensitive whistleblowing data.</p>
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-4 sm:gap-6 md:gap-6 lg:gap-6 xl:gap-6 w-full px-2 sm:px-4 md:px-8 lg:px-12 xl:px-25 mt-4">
      {adminSecurities.map((security, index) => (
        <AdminSecurityCard key={index} {...security} />
      ))}
    </div>
  </section>
);

// Testimonials Section Component
export const AdminTestimonials: React.FC = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const testimonialsRef = useRef<HTMLDivElement>(null);

  const goToNextTestimonial = useCallback(() => {
    if (isAnimating || !testimonialsRef.current) return;

    setIsAnimating(true);

    // If we're at the last testimonial, prepare to loop back
    if (currentTestimonial === adminTestimonials.length - 1) {
      // Clone the first testimonial and append it temporarily
      const firstClone = testimonialsRef.current.children[0].cloneNode(true);
      testimonialsRef.current.appendChild(firstClone);

      // Animate to this clone
      testimonialsRef.current.style.transition = "transform 500ms ease-in-out";
      testimonialsRef.current.style.transform = `translateX(-${(currentTestimonial + 1) * 100}%)`;

      // After animation completes, jump back to first without animation
      setTimeout(() => {
        testimonialsRef.current!.style.transition = "none";
        testimonialsRef.current!.style.transform = "translateX(0)";
        // Remove the clone
        testimonialsRef.current!.removeChild(testimonialsRef.current!.lastChild!);
        setCurrentTestimonial(0);

        // Re-enable transitions after a brief delay
        setTimeout(() => {
          testimonialsRef.current!.style.transition = "transform 500ms ease-in-out";
          setIsAnimating(false);
        }, 50);
      }, 500);
    } else {
      // Normal next slide behavior
      setCurrentTestimonial(prev => prev + 1);
      setTimeout(() => {
        setIsAnimating(false);
      }, 500);
    }
  }, [currentTestimonial, isAnimating]);

  useEffect(() => {
    const interval = setInterval(goToNextTestimonial, 5000);
    return () => clearInterval(interval);
  }, [goToNextTestimonial]);

  return (
    <section className="mx-auto px-4 sm:px-8 md:px-20 xl:px-56 py-12 sm:py-16 md:py-20 flex flex-col items-center">
      <div className="relative w-full overflow-hidden">
        <div
          ref={testimonialsRef}
          className="flex"
          style={{
            transform: `translateX(-${currentTestimonial * 100}%)`,
            transition: "transform 500ms ease-in-out"
          }}
        >
          {adminTestimonials.map((testimonial, index) => (
            <div
              key={index}
              className="w-full flex-shrink-0"
            >
              <AdminTestimonialCard {...testimonial} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// CTA Section Component
export const AdminCTA: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-3 sm:mb-4 text-center">Ready to Take Control?</p>
    <p className="px-2 sm:px-4 md:px-20 lg:px-16 xl:px-55 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563] mb-6 sm:mb-7 md:mb-8 text-center">Experience the power of our Admin Portal and see how it can transform your whistleblowing program management.</p>
    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-none">
      <Button
        variant="outline"
        className="w-full sm:w-fit px-4 sm:px-6 py-4 sm:py-6 text-sm sm:text-base md:text-lg text-[#1E4841] bg-[#BBF49C] hover:text-gray-900 border-1 border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 transition-all duration-300 shadow-sm font-semibold"
        aria-label="Request a demo of the whistleblower platform"
      >
        Experience the Admin Console
      </Button>
      <Button
        variant="outline"
        className="w-full sm:w-fit px-4 sm:px-6 py-4 sm:py-6 text-sm sm:text-base md:text-lg text-[#1E4841] hover:bg-[#BBF49C] hover:text-gray-900 border-1 border-[#1E4841] transition-all duration-300 shadow-sm font-semibold"
        aria-label="Contact sales for more information"
      >
        Request a Custom Demo
      </Button>
    </div>
  </section>
);