import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const reportData = await request.json();
    
    // Validate required fields
    if (!reportData.title || !reportData.description || !reportData.category) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Create submitted report
    const submittedReport = await DataService.createReport({
      ...reportData,
      userId: request.user!.id,
      status: 'New',
      isDraft: false,
      dateSubmitted: new Date(),
      lastUpdated: new Date()
    });
    
    return NextResponse.json({
      success: true,
      data: submittedReport,
      message: 'Report submitted successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Submit report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit report' },
      { status: 500 }
    );
  }
});