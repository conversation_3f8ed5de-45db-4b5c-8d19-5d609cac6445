import nodemailer from 'nodemailer';

interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html?: string;
}

export class EmailService {
  private static transporter = nodemailer.createTransport({
    host: process.env.EMAIL_SERVER_HOST,
    port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
    secure: process.env.EMAIL_SERVER_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD,
    },
  });

  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // Use environment variables for the from address
      const from = process.env.EMAIL_FROM || '<EMAIL>';
      
      await this.transporter.sendMail({
        from,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      });
      
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  static async sendAccountUnlockEmail(email: string, token: string): Promise<boolean> {
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const unlockUrl = `${appUrl}/unlock-account?token=${token}`;
    
    const subject = 'Unlock Your Account';
    const text = `
      Your account has been locked due to too many failed login attempts.
      
      To unlock your account, please click the link below:
      ${unlockUrl}
      
      This link will expire in 1 hour.
      
      If you did not request this, please ignore this email.
    `;
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Account Locked</h2>
        <p>Your account has been locked due to too many failed login attempts.</p>
        <p>To unlock your account, please click the button below:</p>
        <p style="text-align: center; margin: 30px 0;">
          <a href="${unlockUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">
            Unlock Account
          </a>
        </p>
        <p>Or copy and paste this URL into your browser:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
          ${unlockUrl}
        </p>
        <p>This link will expire in 1 hour.</p>
        <p>If you did not request this, please ignore this email.</p>
      </div>
    `;
    
    return await this.sendEmail({
      to: email,
      subject,
      text,
      html,
    });
  }
}