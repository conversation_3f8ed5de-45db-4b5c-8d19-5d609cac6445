import { config } from 'dotenv';
import { reseedDatabase } from '../src/lib/db/refined-seed';

// Load environment variables
config({ path: '.env.local' });

async function runRefinedSeed() {
  try {
    console.log('🚀 Starting refined database reseeding...');
    console.log('⚠️  This will clear all existing data and create new consistent data');
    
    const result = await reseedDatabase();
    
    console.log('\n✅ Refined seeding completed successfully!');
    console.log('📊 Result:', JSON.stringify(result, null, 2));
    
    console.log('\n🔑 Login Credentials:');
    console.log('='.repeat(50));
    console.log('ADMINS:');
    console.log('  <EMAIL> / admin123');
    console.log('  <EMAIL> / admin123');
    console.log('\nINVESTIGATORS:');
    console.log('  <EMAIL> / investigator123');
    console.log('  <EMAIL> / investigator123');
    console.log('\nWHISTLEBLOWERS:');
    console.log('  <EMAIL> / whistleblower123');
    console.log('  <EMAIL> / whistleblower123');
    console.log('  <EMAIL> / whistleblower123');
    console.log('  <EMAIL> / whistleblower123');
    console.log('='.repeat(50));
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Refined seeding failed:', error);
    process.exit(1);
  }
}

runRefinedSeed();