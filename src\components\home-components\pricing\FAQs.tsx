"use client";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { faqData } from "@/lib/mockData";

export default function FAQs() {
  const [showMore, setShowMore] = useState(false);

  const renderAccordionItems = (start: number, end: number) => (
    faqData.slice(start, end).map(faq => (
      <AccordionItem key={faq.id} value={`item-${faq.id}`}>
        <AccordionTrigger className="font-bold xl:text-base cursor-pointer">{faq.question}</AccordionTrigger>
        <AccordionContent className="font-semibold xl:text-sm">
          {faq.answer}
        </AccordionContent>
      </AccordionItem>
    ))
  );

  return (
    <section aria-labelledby="FAQs" className="flex flex-col items-center justify-between w-full bg-white py-10 lg:py-10 xl:py-16 px-6 md:px-20 lg:px-44">
      <p className="font-semibold text-xs md:text-base xl:text-lg">Our Pricing Features</p>
      <p className="font-bold text-base md:text-lg lg:text-2xl xl:text-[28px]">Frequently asked questions</p>
      <div className="w-full flex flex-col lg:flex-row justify-between gap-0 lg:gap-16 xl:gap-32 mt-2 lg:mt-4 xl:mt-14">
        <div className="w-full lg:w-1/2 flex flex-col items-center justify-between">
          <Accordion type="single" collapsible className="w-full">
            {renderAccordionItems(0, 6)}
          </Accordion>
        </div>
        <div className={`w-full lg:w-1/2 flex flex-col items-center justify-between ${!showMore ? 'hidden' : 'flex'} lg:flex`}>
          <Accordion type="single" collapsible className="w-full">
            {renderAccordionItems(6, 12)}
          </Accordion>
        </div>
        <Button
          variant="outline"
          className="w-fit mx-auto lg:hidden mt-4 px-8 py-6 text-base md:text-xl text-[#1E4841] hover:bg-[#BBF49C] hover:text-gray-900 border-1 border-[#1E4841] transition-all duration-300 font-semibold"
          aria-label="View more FAQs"
          onClick={() => setShowMore(!showMore)}
        >
          {showMore ? 'View Less' : 'View More'}
        </Button>
      </div>
    </section>
  );
}