"use client";

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';

export default function TestLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const { login, user } = useAuth();

  const handleTestLogin = async () => {
    setIsLoading(true);
    setMessage('');
    
    try {
      const success = await login('<EMAIL>', 'whistleblower123', 'whistleblower');
      if (success) {
        setMessage('Login successful! Should redirect to dashboard...');
      } else {
        setMessage('Login failed');
      }
    } catch (error) {
      setMessage(`Login error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-4">
        <h1 className="text-2xl font-bold text-center">Test Login</h1>
        
        {user ? (
          <div className="p-4 bg-green-100 rounded">
            <p className="text-green-800">User is logged in:</p>
            <pre className="text-sm mt-2">{JSON.stringify(user, null, 2)}</pre>
          </div>
        ) : (
          <div className="p-4 bg-gray-100 rounded">
            <p>No user logged in</p>
          </div>
        )}
        
        <Button 
          onClick={handleTestLogin} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Logging in...' : 'Test Login'}
        </Button>
        
        {message && (
          <div className="p-4 bg-blue-100 rounded">
            <p className="text-blue-800">{message}</p>
          </div>
        )}
      </div>
    </div>
  );
}