"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { Notification } from '@/lib/types';
import { apiClient } from '@/lib/api/client';

interface UseNotificationsReturn {
    notifications: Notification[];
    unreadCount: number;
    isLoading: boolean;
    error: string | null;
    markAsRead: (notificationId: string) => Promise<void>;
    markAllAsRead: () => Promise<void>;
    refresh: () => void;
}

export function useNotifications(): UseNotificationsReturn {
    const { user } = useAuth();
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchNotifications = useCallback(async () => {
        if (!user?.id) {
            setNotifications([]);
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            setError(null);

            const data = await apiClient.get(`/api/notifications?userId=${user.id}&limit=10`);

            if (data.success) {
                setNotifications(data.data || []);
            } else {
                setError(data.error || 'Failed to fetch notifications');
                setNotifications([]);
            }
        } catch (error) {
            console.error('Error fetching notifications:', error);
            setError(error instanceof Error ? error.message : 'Failed to fetch notifications');
            setNotifications([]);
        } finally {
            setIsLoading(false);
        }
    }, [user?.id]);

    useEffect(() => {
        fetchNotifications();
        
        // Refresh notifications every minute
        const interval = setInterval(fetchNotifications, 60000);
        
        return () => clearInterval(interval);
    }, [fetchNotifications]);

    const markAsRead = async (notificationId: string) => {
        try {
            await apiClient.put(`/api/notifications/${notificationId}/mark-read`);

            setNotifications(prev => 
                prev.map(notification => 
                    notification._id === notificationId 
                        ? { ...notification, status: 'read' as const }
                        : notification
                )
            );
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    };

    const markAllAsRead = async () => {
        try {
            await apiClient.put(`/api/notifications/mark-all-read`, { userId: user?.id });

            setNotifications(prev => 
                prev.map(notification => ({ ...notification, status: 'read' as const }))
            );
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    };

    const refresh = () => {
        fetchNotifications();
    };

    const unreadCount = notifications.filter(n => n.status === 'unread').length;

    return {
        notifications,
        unreadCount,
        isLoading,
        error,
        markAsRead,
        markAllAsRead,
        refresh
    };
}