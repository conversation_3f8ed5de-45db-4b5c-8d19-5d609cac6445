import { NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { User } from '@/lib/db/models';

export const runtime = 'nodejs';

export async function GET() {
  try {
    await connectDB();
    
    const users = await User.find({}).select('email firstName lastName role companyId isActive');
    
    return NextResponse.json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    console.error('Test users API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}