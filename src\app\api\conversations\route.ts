import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';
    
    // Get conversations for the authenticated user with company isolation
    let conversations = await DataService.getConversations(
      request.user!.id, 
      request.user!.companyId
    );
    
    // If unreadOnly is true, filter for conversations with unread messages
    if (unreadOnly) {
      // For now, since we don't have a proper unread message tracking system,
      // let's return an empty array to prevent false unread counts
      conversations = [];
    } else {
      // Apply limit if specified
      if (limit && limit > 0) {
        conversations = conversations.slice(0, limit);
      }
    }
    
    return NextResponse.json({
      success: true,
      data: conversations
    });
  } catch (error) {
    console.error('Conversations GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { reportId, participants } = await request.json();
    
    if (!reportId || !participants || !Array.isArray(participants)) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: reportId and participants' },
        { status: 400 }
      );
    }
    
    // Ensure the current user is included in participants
    const allParticipants = [...new Set([request.user!.id, ...participants])];
    
    // Create the conversation
    const conversation = await DataService.createConversation({
      reportId,
      participants: allParticipants
    });
    
    return NextResponse.json({
      success: true,
      data: conversation
    }, { status: 201 });
  } catch (error) {
    console.error('Conversations POST API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});