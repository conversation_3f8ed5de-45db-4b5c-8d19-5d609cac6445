// This script adds Suspense wrappers to all client components that use useSearchParams
// Run with: pnpm tsx scripts/add-suspense-wrappers.ts

import fs from 'fs';
import path from 'path';

// Pattern to detect useSearchParams usage
const useSearchParamsPattern = /useSearchParams\(\)/;

// Pattern to detect if Suspense is already imported
const suspenseImportPattern = /import [^;]*Suspense[^;]*from ['"]react['"]/;

// Function to add Suspense import if needed
function addSuspenseImport(content: string): string {
  if (suspenseImportPattern.test(content)) {
    return content;
  }
  
  // Check if there's a React import
  if (content.includes("import React")) {
    return content.replace(/import React([^;]*);/, `import React$1, { Suspense };`);
  } else if (content.includes("import {")) {
    return content.replace(/import \{([^}]*)\} from ['"]react['"]/g, `import { Suspense, $1 } from 'react'`);
  } else {
    // Add a new import
    return content.replace(/(import [^;]*;)/, `$1\nimport { Suspense } from 'react';`);
  }
}

// Function to wrap component with Suspense
function wrapComponentWithSuspense(content: string): string {
  // Extract the component name
  const componentNameMatch = content.match(/export default function ([A-Za-z0-9_]+)/);
  if (!componentNameMatch) return content;
  
  const componentName = componentNameMatch[1];
  const innerComponentName = `${componentName}Content`;
  
  // Check if component is already wrapped
  if (content.includes(`function ${innerComponentName}`)) {
    return content;
  }
  
  // Find the component definition
  const componentDefStart = content.indexOf(`export default function ${componentName}`);
  const componentDefEnd = content.lastIndexOf('}');
  
  if (componentDefStart === -1 || componentDefEnd === -1) return content;
  
  const componentDef = content.substring(componentDefStart, componentDefEnd + 1);
  
  // Create the inner component
  const innerComponentDef = componentDef
    .replace(`export default function ${componentName}`, `function ${innerComponentName}`)
    .replace(/return \(\s*(<[^>]+>)/, (match, p1) => {
      // Remove any outer div/fragment if it's just a wrapper
      return `return (${p1}`;
    });
  
  // Create the outer component with Suspense
  const outerComponentDef = `export default function ${componentName}() {
  return (
    <Suspense fallback={<div className="animate-pulse p-4">Loading...</div>}>
      <${innerComponentName} />
    </Suspense>
  );
}`;
  
  // Replace the original component with both components
  return content.substring(0, componentDefStart) + 
         innerComponentDef + 
         '\n\n' + 
         outerComponentDef;
}

// Function to process a file
function processFile(filePath: string): void {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Skip if not a client component or doesn't use useSearchParams
  if (!content.includes('"use client"') || !useSearchParamsPattern.test(content)) {
    return;
  }
  
  console.log(`Processing: ${filePath}`);
  
  // Add Suspense import if needed
  let newContent = addSuspenseImport(content);
  
  // Wrap component with Suspense
  newContent = wrapComponentWithSuspense(newContent);
  
  // Write the updated content back to the file
  fs.writeFileSync(filePath, newContent);
  console.log(`✓ Updated: ${filePath}`);
}

// Function to recursively process directories
function processDirectory(dirPath: string): void {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      processDirectory(fullPath);
    } else if (entry.name.endsWith('.tsx') || entry.name.endsWith('.jsx')) {
      processFile(fullPath);
    }
  }
}

// Start processing
console.log('Adding Suspense wrappers to client components that use useSearchParams...');
processDirectory(path.join(process.cwd(), 'src', 'app'));
console.log('Done!');