import { NextResponse } from 'next/server';

export const runtime = 'nodejs';

export async function GET() {
  try {
    // Hardcoded system test - no database required
    console.log('✅ Hardcoded authentication system operational');
    
    return NextResponse.json({
      success: true,
      message: 'Hardcoded authentication system test successful',
      details: {
        authentication: 'Hardcoded credentials working',
        userSystem: 'Local storage based authentication',
        dataStorage: 'In-memory hardcoded data',
        status: 'All systems operational'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ System test failed:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'System test failed', 
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}