import { NextResponse } from 'next/server';
import { seedDatabase } from '@/lib/db/seed';

export async function POST() {
  try {
    // Temporary endpoint for initial database seeding (no auth required)
    console.log('Starting database seeding...');
    const result = await seedDatabase();
    console.log('Database seeding completed successfully');
    
    return NextResponse.json({ 
      success: true, 
      message: 'Database seeded successfully',
      data: result
    });
  } catch (error) {
    console.error('Database seeding failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database seeding failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}