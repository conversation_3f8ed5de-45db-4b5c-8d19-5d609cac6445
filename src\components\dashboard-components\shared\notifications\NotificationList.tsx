"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Bell } from "lucide-react";
import { Notification } from "@/lib/types";
import NotificationItem from "./NotificationItem";

interface NotificationListProps {
    notifications: Notification[];
    filteredCount: number;
    hasActiveFilters: boolean;
    currentPage: number;
    totalPages: number;
    startIndex: number;
    endIndex: number;
    onMarkAsRead: (id: string) => void;
    onDelete: (id: string) => void;
    onPageChange: (page: number) => void;
    onClearFilters: () => void;
}

export default function NotificationList({
    notifications,
    filteredCount,
    hasActiveFilters,
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    onMarkAsRead,
    onDelete,
    onPage<PERSON><PERSON><PERSON>,
    onClearFilters
}: NotificationListProps) {
    return (
        <Card>
            <CardHeader className="pb-3 sm:pb-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
                    <CardTitle className="text-base sm:text-lg">
                        All Notifications ({filteredCount})
                    </CardTitle>
                    {hasActiveFilters && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onClearFilters}
                            className="text-xs sm:text-sm w-full sm:w-auto"
                        >
                            Clear Filters
                        </Button>
                    )}
                </div>
            </CardHeader>
            <CardContent className="p-0">
                {notifications.length > 0 ? (
                    <div className="divide-y divide-gray-200">
                        {notifications.map((notification) => (
                            <NotificationItem
                                key={notification._id || (notification as Notification & { id?: string }).id || `temp-${Math.random()}`}
                                notification={notification}
                                onMarkAsRead={onMarkAsRead}
                                onDelete={onDelete}
                            />
                        ))}
                    </div>
                ) : (
                    <div className="py-8 sm:py-12 text-center px-4">
                        <Bell className="w-8 h-8 sm:w-12 sm:h-12 text-gray-300 mx-auto mb-3 sm:mb-4" />
                        <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
                        <p className="text-sm sm:text-base text-gray-500 max-w-md mx-auto">
                            {hasActiveFilters
                                ? "No notifications match your current filters."
                                : "You're all caught up! No notifications to show."
                            }
                        </p>
                    </div>
                )}

                {totalPages > 1 && (
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-3 sm:p-4 border-t border-gray-200">
                        <p className="text-xs sm:text-sm text-gray-500 text-center sm:text-left">
                            Showing {startIndex + 1} to {Math.min(endIndex, filteredCount)} of {filteredCount} notifications
                        </p>
                        <Pagination className="justify-center sm:justify-end">
                            <PaginationContent>
                                <PaginationItem>
                                    <PaginationPrevious
                                        onClick={(e) => {
                                            e.preventDefault();
                                            if (currentPage > 1) onPageChange(currentPage - 1);
                                        }}
                                        className={`${currentPage === 1 ? "pointer-events-none opacity-50" : ""} text-xs sm:text-sm`}
                                    />
                                </PaginationItem>
                                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                                    let page;
                                    if (totalPages <= 5) {
                                        page = i + 1;
                                    } else if (currentPage <= 3) {
                                        page = i + 1;
                                    } else if (currentPage >= totalPages - 2) {
                                        page = totalPages - 4 + i;
                                    } else {
                                        page = currentPage - 2 + i;
                                    }
                                    return (
                                        <PaginationItem key={page}>
                                            <PaginationLink
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    onPageChange(page);
                                                }}
                                                isActive={currentPage === page}
                                                className="text-xs sm:text-sm"
                                            >
                                                {page}
                                            </PaginationLink>
                                        </PaginationItem>
                                    );
                                })}
                                <PaginationItem>
                                    <PaginationNext
                                        onClick={(e) => {
                                            e.preventDefault();
                                            if (currentPage < totalPages) onPageChange(currentPage + 1);
                                        }}
                                        className={`${currentPage === totalPages ? "pointer-events-none opacity-50" : ""} text-xs sm:text-sm`}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}