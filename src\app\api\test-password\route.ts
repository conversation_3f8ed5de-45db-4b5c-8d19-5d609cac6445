import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { User } from '@/lib/db/models';
import bcrypt from 'bcrypt';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    await connectDB();
    
    const user = await User.findOne({ email, isActive: true });
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Type assertion to access user properties
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const userData = user as any;
    
    console.log('Testing password for:', email);
    console.log('Stored hash:', userData.hashedPassword);
    console.log('Password to test:', password);
    
    const isValid = await bcrypt.compare(password, userData.hashedPassword);
    
    console.log('Password comparison result:', isValid);
    
    return NextResponse.json({
      success: true,
      data: {
        email: userData.email,
        passwordValid: isValid,
        hashLength: userData.hashedPassword?.length
      }
    });
  } catch (error) {
    console.error('Test password API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}