import mongoose from 'mongoose';
import connectDB from './mongodb';
import { Report, Notification, Conversation, Message, Blog, User, Company, PricingPlan } from './models';
import { ReportFilters, NotificationFilters, User as UserType, UserInput } from '@/lib/types';
import { randomBytes, createHash } from 'crypto';
import bcrypt from 'bcrypt';
import { UserDocument, ReportDocument, BlogDocument, PricingPlanDocument } from './models/interfaces';
import { encryptMessage, decryptMessage, encryptHtmlContent, decryptHtmlContent } from '@/lib/encryption/messageEncryption';

export class DataService {
  // User related methods
  static async getUserById(id: string) {
    await connectDB();
    return await User.findById(id).select('-hashedPassword') as UserDocument;
  }

  static async getUserByEmail(email: string) {
    await connectDB();
    return await User.findOne({ email }) as UserDocument;
  }

  static async createUser(userData: UserInput) {
    await connectDB();
    
    const userDoc: Partial<UserType> = {
      ...userData,
      isActive: userData.isActive ?? true,
      role: userData.role || 'whistleblower'
    };
    
    // Hash password if provided
    if (userData.password) {
      userDoc.hashedPassword = await this.hashPassword(userData.password);
    }
    
    const user = new User(userDoc);
    return await user.save();
  }

  static async updateUser(id: string, updateData: Partial<UserType>) {
    await connectDB();
    return await User.findByIdAndUpdate(id, updateData, { new: true }).select('-hashedPassword');
  }

  static async authenticateUser(email: string, password: string) {
    try {
      await connectDB();
      const user = await User.findOne({ email }) as UserDocument;
      
      if (!user) {
        return { error: 'Invalid email or password' };
      }
      
      // Check if account is inactive
      if (user.isActive === false) {
        return { error: 'Account is inactive. Please contact support.' };
      }
      
      // Check if account is locked
      if (user.accountLocked && user.accountLockedUntil && new Date() < user.accountLockedUntil) {
        const minutesLeft = Math.ceil((user.accountLockedUntil.getTime() - Date.now()) / (60 * 1000));
        return { 
          error: `Account is temporarily locked due to too many failed login attempts. Please try again in ${minutesLeft} minute${minutesLeft !== 1 ? 's' : ''}.` 
        };
      }
      
      let passwordMatch = false;
      
      // Handle password migration from SHA-256 to bcrypt
      if (user.passwordNeedsMigration && user.passwordHashAlgorithm === 'sha256') {
        // Verify with old SHA-256 method
        const salt = process.env.PASSWORD_SALT || 'whistleblower-security-salt';
        const oldHash = createHash('sha256').update(password + salt).digest('hex');
        
        if (user.hashedPassword === oldHash) {
          passwordMatch = true;
          
          // Migrate to bcrypt
          const bcryptHash = await this.hashPassword(password);
          user.hashedPassword = bcryptHash;
          user.passwordNeedsMigration = false;
          user.passwordHashAlgorithm = 'bcrypt';
          await user.save();
        }
      } else {
        // Standard bcrypt comparison
        passwordMatch = await this.comparePassword(password, user.hashedPassword);
      }
      
      if (!passwordMatch) {
        // Increment failed login attempts
        user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
        
        // Lock account after 5 failed attempts
        if (user.failedLoginAttempts >= 5) {
          user.accountLocked = true;
          user.accountLockedUntil = new Date(Date.now() + 30 * 60 * 1000); // Lock for 30 minutes
          await user.save();
          return { error: 'Account locked due to too many failed login attempts. Please try again in 30 minutes.' };
        }
        
        await user.save();
        return { error: 'Invalid email or password' };
      }
      
      // Reset failed login attempts on successful login
      if (user.failedLoginAttempts > 0 || user.accountLocked) {
        user.failedLoginAttempts = 0;
        user.accountLocked = false;
        user.accountLockedUntil = undefined;
      }
      
      user.lastLogin = new Date();
      user.lastActive = new Date();
      await user.save();
      
      return { user };
    } catch (error) {
      console.error('Authentication error:', error);
      return { error: 'An error occurred during authentication' };
    }
  }

  static async generateTwoFactorCode(userId: string) {
    try {
      await connectDB();
      const user = await User.findById(userId) as UserDocument;
      
      if (!user) {
        console.error('User not found for 2FA code generation:', userId);
        return null;
      }
      
      // Generate a 6-digit code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
      
      // Save to user's two-factor auth data
      user.twoFactor = {
        enabled: user.twoFactor?.enabled || false,
        method: user.twoFactor?.method || 'email',
        secret: user.twoFactor?.secret,
        backupCodes: user.twoFactor?.backupCodes,
        verificationCode,
        verificationCodeExpires: expiresAt,
        attempts: 0 // Reset attempts counter
      };
      
      await user.save();
      return { verificationCode, expiresAt };
    } catch (error) {
      console.error('Error generating 2FA code:', error);
      return null;
    }
  }

  static async verifyTwoFactorCode(userId: string, code: string) {
    try {
      await connectDB();
      const user = await User.findById(userId) as UserDocument;
      
      if (!user) {
        console.error('User not found for 2FA verification:', userId);
        return false;
      }
      
      if (!user.twoFactor?.verificationCode) {
        console.error('No verification code found for user:', userId);
        return false;
      }
      
      // Check if too many attempts
      if (user.twoFactor.attempts && user.twoFactor.attempts >= 5) {
        // Clear verification code and require a new one
        user.twoFactor.verificationCode = undefined;
        user.twoFactor.verificationCodeExpires = undefined;
        await user.save();
        return false;
      }
      
      // Check if code is expired
      if (user.twoFactor.verificationCodeExpires && 
          new Date() > user.twoFactor.verificationCodeExpires) {
        return false;
      }
      
      // Check if code matches
      if (user.twoFactor.verificationCode !== code) {
        // Increment failed attempts
        user.twoFactor.attempts = (user.twoFactor.attempts || 0) + 1;
        await user.save();
        return false;
      }
      
      // Clear verification code after successful verification
      user.twoFactor.verificationCode = undefined;
      user.twoFactor.verificationCodeExpires = undefined;
      user.twoFactor.attempts = 0;
      await user.save();
      
      return true;
    } catch (error) {
      console.error('Error verifying 2FA code:', error);
      return false;
    }
  }

  // Company related methods
  static async getCompanyById(id: string) {
    await connectDB();
    return await Company.findById(id);
  }

  static async createCompany(companyData: Record<string, unknown>) {
    await connectDB();
    const company = new Company(companyData);
    return await company.save();
  }

  static async updateCompany(id: string, updateData: Record<string, unknown>) {
    await connectDB();
    return await Company.findByIdAndUpdate(id, updateData, { new: true });
  }

  // Report related methods
  static async getReports(userId?: string, filters?: ReportFilters, companyId?: string) {
    await connectDB();
    
    const query: Record<string, unknown> = {};
    
    // Company-wise isolation: if companyId is provided, filter by company
    if (companyId) {
      // Get users from the same company
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      query.userId = { $in: companyUserIds };
    } else if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      // If no company filter but userId provided, filter by user
      query.userId = userId;
    }
    
    if (filters?.status) query.status = { $in: filters.status };
    if (filters?.category) query.category = { $in: filters.category };
    if (filters?.priority) query.priority = { $in: filters.priority };
    
    const reports = await Report.find(query)
      .populate('assignedInvestigator', 'firstName lastName')
      .populate('userId', 'firstName lastName email companyId')
      .sort({ createdAt: -1 })
      .limit(filters?.limit || 10)
      .skip(filters?.offset || 0);
    
    // Ensure all reports have a valid status
    return reports.map(report => {
      const typedReport = report.toObject() as unknown as ReportDocument;
      const reportAny = report as { status?: string };
      // Set default status if undefined
      if (!reportAny.status) reportAny.status = 'New';
      return typedReport;
    });
  }

  static async getReportById(id: string) {
    await connectDB();
    const report = await Report.findById(id).populate('assignedInvestigator', 'firstName lastName');
    if (!report) return null;
    return report as unknown as ReportDocument;
  }

  static async createReport(reportData: Partial<ReportDocument>) {
    await connectDB();
    
    // Generate a unique report ID if not provided
    if (!reportData.reportId) {
      const reportCount = await Report.countDocuments();
      const reportId = `WB-${new Date().getFullYear()}-${(reportCount + 1).toString().padStart(4, '0')}`;
      reportData.reportId = reportId;
    }
    
    const report = new Report(reportData);
    const savedReport = await report.save() as unknown as ReportDocument;
    
    // Create a notification for the report creation
    if (reportData.userId) {
      await this.createNotification({
        userId: reportData.userId,
        type: 'report_update',
        title: 'Report Created',
        message: `Your report ${savedReport.reportId} has been created successfully.`,
        reportId: savedReport._id,
        priority: 'medium'
      });
    }
    
    // Create a conversation for the report
    try {
      await this.createConversationForReport(savedReport._id.toString(), reportData.userId?.toString());
    } catch (error) {
      console.error('Error creating conversation for report:', error);
      // Don't fail the report creation if conversation creation fails
    }
    
    // Trigger real-time stats update
    try {
      const { triggerStatsUpdateOnReportCreate } = await import('@/lib/realtime/statsUpdater');
      await triggerStatsUpdateOnReportCreate({
        reportId: savedReport.reportId,
        title: savedReport.title,
        priority: savedReport.priority,
        userId: savedReport.userId,
        companyId: savedReport.companyId
      });
    } catch (error) {
      console.error('Error triggering real-time stats update:', error);
    }
    
    return savedReport;
  }

  static async updateReport(id: string, updateData: Partial<ReportDocument>) {
    await connectDB();
    
    // Get the original report for comparison
    const originalReport = await Report.findById(id);
    const oldStatus = (originalReport as { status?: string })?.status;
    
    const updatedReport = await Report.findByIdAndUpdate(id, updateData, { new: true });
    const typedReport = updatedReport as unknown as ReportDocument;
    
    // Create a notification for status updates
    if (updatedReport && updateData.status) {
      // Cast to ReportDocument type
      const reportDoc = updatedReport as unknown as ReportDocument;
      if (reportDoc.userId) {
        await this.createNotification({
          userId: reportDoc.userId,
          type: 'report_update',
          title: 'Report Status Updated',
          message: `Your report ${reportDoc.reportId} status has been updated to ${updateData.status}.`,
          reportId: updatedReport._id,
          priority: 'medium'
        });
      }
    }
    
    // Trigger real-time stats update for status changes
    if (updatedReport && updateData.status && oldStatus !== updateData.status) {
      try {
        const { triggerStatsUpdateOnStatusChange } = await import('@/lib/realtime/statsUpdater');
        await triggerStatsUpdateOnStatusChange(typedReport, oldStatus, updateData.status);
      } catch (error) {
        console.error('Error triggering real-time stats update on status change:', error);
      }
    }
    
    return typedReport;
  }

  static async deleteReport(id: string) {
    await connectDB();
    return await Report.findByIdAndDelete(id);
  }

  static async getReportCount(filters?: Record<string, unknown>) {
    await connectDB();
    return await Report.countDocuments(filters || {});
  }

  static async getRecentActivity(userId?: string, companyId?: string, limit: number = 10) {
    await connectDB();
    
    const query: Record<string, unknown> = {};
    
    // Company-wise isolation: if companyId is provided, filter by company
    if (companyId) {
      // Get users from the same company
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      query.userId = { $in: companyUserIds };
    } else if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      // If no company filter but userId provided, filter by user
      query.userId = userId;
    }
    
    // Get recent reports as activities
    const recentReports = await Report.find(query)
      .populate('userId', 'firstName lastName email')
      .populate('assignedInvestigator', 'firstName lastName')
      .sort({ updatedAt: -1, createdAt: -1 })
      .limit(limit);
    
    // Transform reports into activity format
    const activities = recentReports.map(report => {
      const reportObj = report.toObject();
      const user = reportObj.userId as { firstName?: string; lastName?: string; email?: string };
      const userName = user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email : 'Unknown User';
      
      // Determine activity type and description based on report status and recent changes
      let action = 'created';
      let description = `Report ${reportObj.reportId} was created`;
      
      if (reportObj.status) {
        switch (reportObj.status) {
          case 'Under Review':
            action = 'updated';
            description = `Report ${reportObj.reportId} is now under review`;
            break;
          case 'Awaiting Response':
            action = 'updated';
            description = `Report ${reportObj.reportId} is awaiting response`;
            break;
          case 'Resolved':
            action = 'resolved';
            description = `Report ${reportObj.reportId} has been resolved`;
            break;
          case 'In Progress':
            action = 'updated';
            description = `Report ${reportObj.reportId} is in progress`;
            break;
          case 'Closed':
            action = 'closed';
            description = `Report ${reportObj.reportId} has been closed`;
            break;
          default:
            action = 'created';
            description = `Report ${reportObj.reportId} was created`;
        }
      }
      
      return {
        id: reportObj._id.toString(),
        type: 'report',
        action,
        description,
        user: userName,
        timestamp: reportObj.updatedAt || reportObj.createdAt,
        reportId: reportObj._id.toString(),
        priority: reportObj.priority,
        status: reportObj.status
      };
    });
    
    return activities;
  }

  static async getDashboardStats(userId?: string, companyId?: string) {
    await connectDB();
    
    const query: Record<string, unknown> = {};
    
    // Company-wise isolation: if companyId is provided, filter by company
    if (companyId) {
      // Get users from the same company
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      query.userId = { $in: companyUserIds };
    } else if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      // If no company filter but userId provided, filter by user
      query.userId = userId;
    }
    
    // Get report counts by status
    const [
      totalReports,
      newReports,
      underReviewReports,
      awaitingResponseReports,
      resolvedReports,
      closedReports
    ] = await Promise.all([
      Report.countDocuments(query),
      Report.countDocuments({ ...query, status: 'New' }),
      Report.countDocuments({ ...query, status: 'Under Review' }),
      Report.countDocuments({ ...query, status: 'Awaiting Response' }),
      Report.countDocuments({ ...query, status: 'Resolved' }),
      Report.countDocuments({ ...query, status: 'Closed' })
    ]);
    
    // Get priority counts
    const [highPriorityReports, mediumPriorityReports, lowPriorityReports] = await Promise.all([
      Report.countDocuments({ ...query, priority: 'High' }),
      Report.countDocuments({ ...query, priority: 'Medium' }),
      Report.countDocuments({ ...query, priority: 'Low' })
    ]);
    
    // Calculate trends (compare with last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentReports = await Report.countDocuments({
      ...query,
      createdAt: { $gte: thirtyDaysAgo }
    });
    
    return {
      totalReports,
      statusCounts: {
        new: newReports,
        underReview: underReviewReports,
        awaitingResponse: awaitingResponseReports,
        resolved: resolvedReports,
        closed: closedReports
      },
      priorityCounts: {
        high: highPriorityReports,
        medium: mediumPriorityReports,
        low: lowPriorityReports
      },
      trends: {
        recentReports,
        percentageChange: 0 // Could be calculated based on previous period
      }
    };
  }

  // Notification related methods
  static async getNotifications(userId: string, filters?: NotificationFilters) {
    await connectDB();
    
    const query: Record<string, unknown> = { userId };
    if (filters?.status) query.status = { $in: filters.status };
    if (filters?.type) query.type = { $in: filters.type };
    
    return await Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(filters?.limit || 10)
      .skip(filters?.offset || 0);
  }

  static async getUnreadNotificationCount(userId: string) {
    await connectDB();
    return await Notification.countDocuments({ userId, status: 'unread' });
  }

  static async markNotificationAsRead(notificationId: string) {
    await connectDB();
    return await Notification.findByIdAndUpdate(
      notificationId, 
      { status: 'read', readAt: new Date() },
      { new: true }
    );
  }

  static async createNotification(notificationData: Record<string, unknown>) {
    await connectDB();
    const notification = new Notification(notificationData);
    const savedNotification = await notification.save();
    
    // Trigger real-time notification update
    try {
      const { RealTimeStatsUpdater } = await import('@/lib/realtime/statsUpdater');
      if (notificationData.userId) {
        await RealTimeStatsUpdater.broadcastNotificationUpdate(
          notificationData.userId.toString(),
          savedNotification
        );
      }
    } catch (error) {
      console.error('Error triggering real-time notification update:', error);
    }
    
    return savedNotification;
  }

  static async markAllNotificationsAsRead(userId: string) {
    await connectDB();
    return await Notification.updateMany(
      { userId, status: 'unread' },
      { status: 'read', readAt: new Date() }
    );
  }

  static async deleteNotification(id: string) {
    await connectDB();
    return await Notification.findByIdAndDelete(id);
  }

  // Conversation related methods
  static async getConversations(userId: string, companyId?: string) {
    await connectDB();
    
    let conversationQuery: Record<string, unknown> = { participants: userId };
    
    // Company-wise isolation for conversations
    if (companyId) {
      // Get users from the same company
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      
      // Find conversations where at least one participant is from the same company
      conversationQuery = {
        participants: { $in: [userId, ...companyUserIds] }
      };
    }
    
    const conversations = await Conversation.find(conversationQuery)
    .populate('reportId', 'reportId title')
    .populate({
      path: 'participants',
      select: 'firstName lastName role companyId',
      match: { _id: { $ne: userId } }
    })
    .sort({ lastMessageAt: -1 });
    
    return conversations.map(conv => {
      const convAny = conv as { participants?: unknown[] };
      if (!convAny.participants) convAny.participants = [];
      return conv;
    });
  }



  static async getConversationByReportId(reportId: string) {
    await connectDB();
    return await Conversation.findOne({ reportId })
      .populate('reportId', 'reportId title')
      .populate('participants', 'firstName lastName role');
  }

  static async updateConversationStatus(id: string, status: 'active' | 'closed' | 'archived') {
    await connectDB();
    return await Conversation.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );
  }

  static async addParticipantToConversation(conversationId: string, userId: string) {
    await connectDB();
    return await Conversation.findByIdAndUpdate(
      conversationId,
      { $addToSet: { participants: userId } },
      { new: true }
    ).populate('participants', 'firstName lastName role');
  }

  static async createConversationForReport(reportId: string, userId?: string) {
    await connectDB();
    
    // Check if conversation already exists for this report
    const existingConversation = await Conversation.findOne({ reportId });
    if (existingConversation) {
      return existingConversation;
    }
    
    // Get the report to access company information
    const report = await Report.findById(reportId).populate('userId', 'companyId');
    if (!report) {
      throw new Error('Report not found');
    }
    
    // Get company admins/investigators to add as participants
    const reportAny = report as any;
    const companyId = reportAny.userId?.companyId;
    
    const participants = [userId].filter(Boolean); // Start with report creator
    
    if (companyId) {
      // Add company admins and investigators as participants
      const companyStaff = await User.find({
        companyId,
        role: { $in: ['admin', 'investigator'] },
        isActive: true
      }).select('_id').limit(3); // Limit to avoid too many participants
      
      const staffIds = companyStaff.map(staff => staff._id.toString());
      participants.push(...staffIds);
    }
    
    // Create the conversation
    const conversation = new Conversation({
      reportId,
      participants: [...new Set(participants)], // Remove duplicates
      status: 'active',
      lastMessageAt: new Date()
    });
    
    return await conversation.save();
  }

  // Message related methods

  static async markMessageAsRead(messageId: string, userId: string) {
    await connectDB();
    
    const message = await Message.findById(messageId);
    if (!message) return null;
    
    // Check if user already marked as read
    const messageDoc = message as {
      readBy?: Array<{ userId: { toString: () => string }; readAt: Date }>;
      save: () => Promise<unknown>;
    };
    
    const alreadyRead = messageDoc.readBy?.some(
      (read: { userId: { toString: () => string } }) => read.userId.toString() === userId
    );
    
    if (!alreadyRead) {
      if (!messageDoc.readBy) {
        messageDoc.readBy = [];
      }
      messageDoc.readBy.push({
        userId: new mongoose.Types.ObjectId(userId),
        readAt: new Date()
      });
      await message.save();
    }
    
    return message;
  }

  static async updateMessage(messageId: string, updateData: { content?: string; htmlContent?: string }) {
    await connectDB();
    
    const updatedMessage = await Message.findByIdAndUpdate(
      messageId,
      {
        ...updateData,
        editedAt: new Date()
      },
      { new: true }
    );
    
    return updatedMessage;
  }

  static async deleteMessage(messageId: string, userId: string) {
    await connectDB();
    
    const message = await Message.findById(messageId);
    const messageDoc = message as unknown as {
      senderId: { toString: () => string };
      isDeleted?: boolean;
      deletedAt?: Date;
      save: () => Promise<unknown>;
    };
    
    if (!message || messageDoc.senderId.toString() !== userId) {
      throw new Error('Unauthorized to delete this message');
    }
    
    // Soft delete
    messageDoc.isDeleted = true;
    messageDoc.deletedAt = new Date();
    (message as unknown as { content: string; htmlContent?: string }).content = 'This message has been deleted';
    (message as unknown as { content: string; htmlContent?: string }).htmlContent = undefined;
    await message.save();
    
    return message;
  }

  static async addReaction(messageId: string, userId: string, emoji: string) {
    await connectDB();
    
    const message = await Message.findById(messageId);
    if (!message) return null;
    
    // Remove existing reaction from this user
    const messageDoc = message as {
      reactions?: Array<{ userId: { toString: () => string }; emoji: string; createdAt: Date }>;
      save: () => Promise<unknown>;
    };
    
    messageDoc.reactions = messageDoc.reactions?.filter(
      (reaction: { userId: { toString: () => string } }) => reaction.userId.toString() !== userId
    ) || [];
    
    // Add new reaction
    messageDoc.reactions.push({
      userId: new mongoose.Types.ObjectId(userId),
      emoji,
      createdAt: new Date()
    });
    
    await message.save();
    return message;
  }

  static async removeReaction(messageId: string, userId: string) {
    await connectDB();
    
    const message = await Message.findById(messageId);
    if (!message) return null;
    
    const messageDoc = message as {
      reactions?: Array<{ userId: { toString: () => string }; emoji: string; createdAt: Date }>;
      save: () => Promise<unknown>;
    };
    
    messageDoc.reactions = messageDoc.reactions?.filter(
      (reaction: { userId: { toString: () => string } }) => reaction.userId.toString() !== userId
    ) || [];
    
    await message.save();
    return message;
  }

  // Blog related methods
  static async getBlogPosts(featured?: boolean, limit = 10, offset = 0): Promise<BlogDocument[]> {
    await connectDB();
    
    const query = featured !== undefined ? { featured } : {};
    
    const blogs = await (Blog as mongoose.Model<BlogDocument>)
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .lean();
      
    return blogs as BlogDocument[];
  }

  static async getBlogBySlug(slug: string): Promise<BlogDocument | null> {
    await connectDB();
    return await (Blog as mongoose.Model<BlogDocument>)
      .findOne({ slug })
      .lean() as BlogDocument | null;
  }

  static async createBlogPost(blogData: Partial<BlogDocument>) {
    await connectDB();
    const blog = new (Blog as mongoose.Model<BlogDocument>)(blogData);
    return (await blog.save()).toObject() as BlogDocument;
  }

  static async updateBlogPost(id: string, updateData: Partial<BlogDocument>) {
    await connectDB();
    return await (Blog as mongoose.Model<BlogDocument>)
      .findByIdAndUpdate(id, updateData, { new: true })
      .lean() as BlogDocument | null;
  }

  static async deleteBlogPost(id: string): Promise<BlogDocument | null> {
    await connectDB();
    return await (Blog as mongoose.Model<BlogDocument>)
      .findByIdAndDelete(id)
      .lean() as BlogDocument | null;
  }

  // Dashboard related methods

  static async getRecentActivity(userId?: string, companyId?: string, limit = 5) {
    await connectDB();
    
    const query: Record<string, unknown> = {};
    
    // Company-wise isolation for recent activity
    if (companyId) {
      // Get users from the same company
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      query.userId = { $in: companyUserIds };
    } else if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      query.userId = userId;
    }
    
    const recentReports = await Report.find(query)
      .sort({ updatedAt: -1 })
      .limit(limit)
      .select('reportId title status updatedAt createdAt priority') as ReportDocument[];
    
    return recentReports.map(report => {
      // Cast to ReportDocument type
      const reportDoc = report as ReportDocument;
      
      // Determine activity type based on report data
      const isNew = new Date(reportDoc.createdAt).getTime() === new Date(reportDoc.updatedAt).getTime();
      const activityType = isNew ? 'case_created' : 'status_update';
      
      // Format time ago
      const timeAgo = this.formatTimeAgo(new Date(reportDoc.updatedAt));
      
      return {
        id: reportDoc._id,
        type: activityType,
        title: isNew 
          ? `New report ${reportDoc.reportId} created`
          : `Report ${reportDoc.reportId} updated`,
        description: isNew 
          ? `Priority: ${reportDoc.priority || 'Normal'}`
          : `Status changed to ${reportDoc.status || 'Unknown'}`,
        time: timeAgo,
        reportId: reportDoc.reportId,
        icon: isNew ? 'case_created' : 'status_update'
      };
    });
  }

  private static formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  }

  static async getDashboardStats(userId?: string, companyId?: string) {
    await connectDB();
    
    const query: Record<string, unknown> = {};
    
    // Company-wise isolation for dashboard stats
    if (companyId) {
      // Get users from the same company
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      query.userId = { $in: companyUserIds };
    } else if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      query.userId = userId;
    }
    
    // Get current period stats
    const totalReports = await Report.countDocuments(query);
    const newReports = await Report.countDocuments({ ...query, status: 'New' });
    const underReviewReports = await Report.countDocuments({ ...query, status: 'Under Review' });
    const awaitingResponseReports = await Report.countDocuments({ ...query, status: 'Awaiting Response' });
    const resolvedReports = await Report.countDocuments({ ...query, status: 'Resolved' });
    const highPriorityReports = await Report.countDocuments({ ...query, priority: 'High' });
    
    // Get previous month stats for comparison
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    
    const previousQuery = { ...query, createdAt: { $lt: lastMonth } };
    const previousTotalReports = await Report.countDocuments(previousQuery);
    const previousNewReports = await Report.countDocuments({ ...previousQuery, status: 'New' });
    const previousResolvedReports = await Report.countDocuments({ ...previousQuery, status: 'Resolved' });
    
    // Calculate percentage changes
    const totalReportsChange = previousTotalReports > 0 
      ? Math.round(((totalReports - previousTotalReports) / previousTotalReports) * 100)
      : 0;
    const newReportsChange = previousNewReports > 0 
      ? Math.round(((newReports - previousNewReports) / previousNewReports) * 100)
      : 0;
    const resolvedReportsChange = previousResolvedReports > 0 
      ? Math.round(((resolvedReports - previousResolvedReports) / previousResolvedReports) * 100)
      : 0;
    
    // Get chart data for the last 6 months
    const chartData = await this.getChartData(query);
    
    return {
      totalReports,
      newReports,
      underReviewReports,
      awaitingResponseReports,
      resolvedReports,
      highPriorityReports,
      periodComparison: {
        totalReportsChange,
        newReportsChange,
        resolvedReportsChange,
        period: 'month'
      },
      chartData,
      lastCalculated: new Date()
    };
  }

  static async getChartData(baseQuery: Record<string, unknown>) {
    await connectDB();
    
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    // Get reports over time (last 6 months)
    const overTimeData = await Report.aggregate([
      {
        $match: {
          ...baseQuery,
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);
    
    // Get status distribution
    const statusData = await Report.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    // Format over time data
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const overTime = [];
    
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      
      const dataPoint = overTimeData.find(d => d._id.year === year && d._id.month === month);
      overTime.push({
        month: monthNames[month - 1],
        reports: dataPoint ? dataPoint.count : 0,
        cases: dataPoint ? dataPoint.count : 0 // For admin charts
      });
    }
    
    // Format status distribution data
    const statusColors = {
      'New': '#BBF49C',
      'Under Review': '#ECF4E9',
      'Awaiting Response': '#BBF49C',
      'Resolved': '#1E4841',
      'In Progress': '#1E4841',
      'Closed': '#E5E6E6'
    };
    
    const statusDistribution = statusData.map(item => ({
      name: item._id || 'Unknown',
      value: item.count,
      fill: statusColors[item._id as keyof typeof statusColors] || '#6B7280'
    }));
    
    return {
      overTime,
      statusDistribution
    };
  }

  // Pricing Plan related methods
  static async getPricingPlans(): Promise<PricingPlanDocument[]> {
    await connectDB();
    const plans = await (PricingPlan as mongoose.Model<PricingPlanDocument>)
      .find({ isActive: true })
      .sort({ order: 1 })
      .lean();
    return plans as PricingPlanDocument[];
  }

  static async getPricingPlanById(id: string): Promise<PricingPlanDocument | null> {
    await connectDB();
    const plan = await (PricingPlan as mongoose.Model<PricingPlanDocument>)
      .findById(id)
      .lean();
    return plan as PricingPlanDocument | null;
  }

  // Account management methods
  static async unlockAccount(userId: string) {
    await connectDB();
    return await User.findByIdAndUpdate(
      userId,
      {
        accountLocked: false,
        accountLockedUntil: undefined,
        failedLoginAttempts: 0
      },
      { new: true }
    ).select('-hashedPassword -sessionToken');
  }

  // OAuth related methods

  static async findOrCreateOAuthUser(profile: { 
    email: string;
    name?: string;
    given_name?: string;
    family_name?: string;
    picture?: string;
    sub?: string;
  }, provider: string) {
    try {
      if (!profile || !profile.email) {
        console.error('Invalid OAuth profile:', profile);
        return { error: 'Invalid OAuth profile data' };
      }
      
      await connectDB();
      
      // Try to find existing user by email
      let user = await User.findOne({ email: profile.email });
      const typedUser = user as unknown as UserDocument;
      
      if (!user) {
        // Extract name parts safely
        let firstName = '', lastName = '';
        if (profile.name) {
          const nameParts = profile.name.split(' ');
          firstName = nameParts[0] || '';
          lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
        } else if (profile.given_name && profile.family_name) {
          // Some providers use given_name and family_name
          firstName = profile.given_name;
          lastName = profile.family_name;
        }
        
        // Create new user if not found
        user = new User({
          email: profile.email,
          firstName,
          lastName,
          role: 'whistleblower', // Default role for OAuth users
          isActive: true,
          provider: provider,
          lastLogin: new Date(),
          lastActive: new Date(),
          emailVerified: true // OAuth users have verified emails
        });
        
        await user.save();
      } else {
        // Update existing user with last login and provider info
        const userAny = user as unknown as UserDocument;
        await User.findByIdAndUpdate(user._id, {
          lastLogin: new Date(),
          lastActive: new Date(),
          provider: provider,
          // If user was inactive, reactivate them
          ...(userAny.isActive === false && { isActive: true })
        });
      }
      
      return typedUser;
    } catch (error) {
      console.error('OAuth user creation/update error:', error);
      return { error: 'Failed to process OAuth authentication' };
    }
  }

  // Messaging methods
  static async createConversation(conversationData: {
    reportId: string;
    participants: string[];
  }) {
    await connectDB();
    const conversation = new Conversation(conversationData);
    return await conversation.save();
  }

  static async createMessage(messageData: {
    conversationId: string;
    senderId: string;
    content: string;
    htmlContent?: string;
    messageType?: 'text' | 'file' | 'system';
    attachments?: Array<{
      fileName: string;
      fileUrl: string;
      fileSize: number;
      mimeType: string;
    }>;
  }) {
    await connectDB();
    
    // Encrypt message content for security
    const encryptedContent = encryptMessage(messageData.content);
    let encryptedHtmlContent;
    
    if (messageData.htmlContent) {
      encryptedHtmlContent = encryptHtmlContent(messageData.htmlContent);
    }
    
    const message = new Message({
      conversationId: messageData.conversationId,
      senderId: messageData.senderId,
      content: encryptedContent.encryptedContent,
      htmlContent: encryptedHtmlContent?.encryptedContent,
      messageType: messageData.messageType || 'text',
      attachments: messageData.attachments,
      timestamp: new Date(),
      isEncrypted: true,
      encryptionData: {
        contentIv: encryptedContent.iv,
        contentTag: encryptedContent.tag,
        htmlContentIv: encryptedHtmlContent?.iv,
        htmlContentTag: encryptedHtmlContent?.tag
      }
    });
    
    const savedMessage = await message.save();
    
    // Update conversation's last message timestamp
    await this.updateConversationLastActivity(messageData.conversationId);
    
    // Trigger real-time updates
    try {
      // Get conversation with participants and report data
      const conversation = await Conversation.findById(messageData.conversationId)
        .populate('participants', '_id')
        .populate('reportId', 'companyId reportId');
      
      if (conversation) {
        // Broadcast new message to participants
        const { RealTimeStatsUpdater, triggerStatsUpdateOnMessage } = await import('@/lib/realtime/statsUpdater');
        const participantIds = ((conversation as unknown as { participants: { _id: { toString: () => string } }[] }).participants).map(p => p._id.toString());
        
        await RealTimeStatsUpdater.broadcastNewMessage(
          messageData.conversationId,
          {
            _id: savedMessage._id,
            content: messageData.content, // Send unencrypted content for real-time display
            senderId: messageData.senderId,
            messageType: messageData.messageType,
            timestamp: (savedMessage as unknown as { timestamp: Date }).timestamp
          },
          participantIds
        );
        
        // Update stats
        await triggerStatsUpdateOnMessage(savedMessage, conversation);
      }
    } catch (error) {
      console.error('Error triggering real-time message updates:', error);
    }
    
    return savedMessage;
  }

  static async getConversationsByUserId(userId: string) {
    await connectDB();
    return await Conversation.find({
      participants: userId
    }).populate('reportId').populate('participants', 'firstName lastName email role');
  }

  static async getMessagesByConversationId(conversationId: string) {
    await connectDB();
    const messages = await Message.find({
      conversationId
    }).populate('senderId', 'firstName lastName email role').sort({ timestamp: 1 });
    
    // Ensure timestamp exists
    messages.forEach(msg => {
      const msgAny = msg as { timestamp?: Date; createdAt?: Date };
      if (!msgAny.timestamp) msgAny.timestamp = msgAny.createdAt || new Date();
    });
    
    // Decrypt messages before returning
    return messages.map(message => {
      const messageObj = message.toObject() as Record<string, unknown>;
      
      if (messageObj.isEncrypted && messageObj.encryptionData) {
        // Decrypt content
        const decryptedContent = decryptMessage({
          encryptedContent: messageObj.content as string,
          iv: (messageObj.encryptionData as Record<string, unknown>).contentIv as string || '',
          tag: (messageObj.encryptionData as Record<string, unknown>).contentTag as string || '',
          isEncrypted: true
        });
        
        messageObj.content = decryptedContent.content;
        
        // Decrypt HTML content if present
        if (messageObj.htmlContent && (messageObj.encryptionData as Record<string, unknown>).htmlContentIv) {
          const decryptedHtmlContent = decryptHtmlContent({
            encryptedContent: messageObj.htmlContent as string,
            iv: (messageObj.encryptionData as Record<string, unknown>).htmlContentIv as string,
            tag: (messageObj.encryptionData as Record<string, unknown>).htmlContentTag as string || '',
            isEncrypted: true
          });
          
          messageObj.htmlContent = decryptedHtmlContent.content;
        }
      }
      
      // Remove encryption data from response for security
      delete messageObj.encryptionData;
      
      return messageObj;
    });
  }

  static async getMessages(conversationId: string) {
    return await this.getMessagesByConversationId(conversationId);
  }

  static async getConversationById(conversationId: string) {
    await connectDB();
    return await Conversation.findById(conversationId)
      .populate('reportId')
      .populate('participants', 'firstName lastName email role');
  }

  static async updateConversationLastActivity(conversationId: string) {
    await connectDB();
    return await Conversation.findByIdAndUpdate(
      conversationId,
      { lastMessageAt: new Date() },
      { new: true }
    );
  }

  static async getCompanyUsers(companyId: string) {
    await connectDB();
    return await User.find({ companyId })
      .select('-hashedPassword -sessionToken')
      .sort({ createdAt: -1 });
  }

  // Utility methods
  static async hashPassword(password: string): Promise<string> {
    // Use bcrypt with automatic salt generation (cost factor 12)
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }
  
  static async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    // Compare password with stored hash using bcrypt
    return await bcrypt.compare(password, hashedPassword);
  }

  static generateToken(): string {
    // Generate a more secure token with additional entropy
    const timestamp = Date.now().toString();
    const random = randomBytes(48).toString('hex'); // Increased from 32 to 48 bytes
    const serverSecret = process.env.SERVER_SECRET || 'whistleblower-server-secret';
    
    return createHash('sha256')
      .update(timestamp + random + serverSecret)
      .digest('hex');
  }
  
  static validateEmail(email: string): boolean {
    // Basic email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  static validatePassword(password: string): { valid: boolean; message?: string } {
    if (!password || password.length < 8) {
      return { valid: false, message: 'Password must be at least 8 characters long' };
    }
    
    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) {
      return { valid: false, message: 'Password must contain at least one uppercase letter' };
    }
    
    // Check for at least one lowercase letter
    if (!/[a-z]/.test(password)) {
      return { valid: false, message: 'Password must contain at least one lowercase letter' };
    }
    
    // Check for at least one number
    if (!/[0-9]/.test(password)) {
      return { valid: false, message: 'Password must contain at least one number' };
    }
    
    // Check for at least one special character
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return { valid: false, message: 'Password must contain at least one special character' };
    }
    
    return { valid: true };
  }


}

export default DataService;

