import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { loginSchema } from '@/lib/schemas';
import { validateCredentials } from '@/lib/auth/hardcoded-users';
import { generateToken } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { email, password, remember } = validationResult.data;

    // First, try to authenticate with database users
    await connectDB();
    const dbAuthResult = await DataService.authenticateUser(email, password);
    
    if (dbAuthResult.user) {
      // Database user found and authenticated
      const user = dbAuthResult.user;
      const token = generateToken(user._id.toString());
      
      return NextResponse.json({
        success: true,
        message: 'Login successful',
        token,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          role: user.role,
          companyId: user.companyId,
          isActive: user.isActive,
          lastLogin: user.lastLogin
        },
        remember
      });
    }

    // If database authentication failed, try hardcoded users
    const hardcodedUser = validateCredentials(email, password);
    if (hardcodedUser) {
      const token = generateToken(hardcodedUser.id);
      
      return NextResponse.json({
        success: true,
        message: 'Login successful (test user)',
        token,
        user: {
          id: hardcodedUser.id,
          email: hardcodedUser.email,
          firstName: hardcodedUser.firstName,
          lastName: hardcodedUser.lastName,
          fullName: hardcodedUser.name,
          role: hardcodedUser.role,
          companyId: hardcodedUser.companyId,
          isActive: hardcodedUser.isActive,
          lastLogin: new Date()
        },
        remember,
        isTestUser: true
      });
    }

    // If both failed, return error
    if (dbAuthResult.error) {
      return NextResponse.json(
        { success: false, error: dbAuthResult.error },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Invalid email or password. Please use the appropriate login page: /login/admin for administrators or /login/whistleblower for whistleblowers.' 
      },
      { status: 401 }
    );

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}