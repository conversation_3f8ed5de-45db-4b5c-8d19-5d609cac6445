"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Search,
    ListFilter,
    X
} from "lucide-react";
import { DataTable } from "../my-reports/data-table";
import { columns } from "./columns";
import { ReportData } from "@/lib/types";
import { ReportDocument } from "@/lib/db/models/interfaces";
import { useAuth } from "@/hooks/useAuth";
import { apiClient } from "@/lib/api/client";

export default function ReportTable() {
    const { user } = useAuth();
    const [reportsData, setReportsData] = useState<ReportData[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");

    // Load reports data
    useEffect(() => {
        const loadReports = async () => {
            if (!user?.id) return;
            
            try {
                setIsLoading(true);
                const data = await apiClient.get('/api/reports?limit=5');
                
                if (data.success) {
                    // Transform API data to match ReportData interface
                    const transformedReports = data.data.map((report: ReportDocument) => {
                        // Helper function to get status color
                        const getStatusColor = (status: string) => {
                            switch (status) {
                                case 'New':
                                    return 'bg-blue-100 text-blue-800';
                                case 'Under Review':
                                    return 'bg-yellow-100 text-yellow-800';
                                case 'Awaiting Response':
                                    return 'bg-orange-100 text-orange-800';
                                case 'Resolved':
                                    return 'bg-green-100 text-green-800';
                                default:
                                    return 'bg-gray-100 text-gray-800';
                            }
                        };

                        // Calculate progress based on status
                        const getProgress = (status: string) => {
                            switch (status) {
                                case 'New':
                                    return 10;
                                case 'Under Review':
                                    return 50;
                                case 'Awaiting Response':
                                    return 75;
                                case 'Resolved':
                                    return 100;
                                default:
                                    return 0;
                            }
                        };

                        const progress = getProgress(report.status);
                        
                        return {
                            id: report.reportId || report._id,
                            title: report.title,
                            status: report.status,
                            statusColor: getStatusColor(report.status),
                            dateSubmitted: new Date(report.createdAt).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                            }),
                            lastUpdated: new Date(report.updatedAt).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                            }),
                            priority: report.priority,
                            category: report.category,
                            progress: `${progress}%`,
                            progressPercentage: progress
                        };
                    });
                    setReportsData(transformedReports);
                }
            } catch (error) {
                console.error('Error loading reports:', error);
                // Fallback to empty array
                setReportsData([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadReports();
    }, [user?.id]);

    // Filter reports based on search and status
    const filteredReports = reportsData.filter(report => {
        const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            report.id.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = statusFilter === "all" || report.status === statusFilter;
        return matchesSearch && matchesStatus;
    });

    // Get unique statuses for filter dropdown
    const uniqueStatuses = [...new Set(reportsData.map(report => report.status))];

    // Clear all filters
    const clearFilters = () => {
        setSearchTerm("");
        setStatusFilter("all");
    };

    // Check if any filters are active
    const hasActiveFilters = searchTerm !== "" || statusFilter !== "all";

    return (
        <Card>
            <CardHeader className="flex flex-col lg:flex-row items-center justify-between gap-4">
                <div>
                    <CardTitle className="text-base sm:text-lg">Your Reports</CardTitle>
                </div>
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                            placeholder="Search reports..."
                            name="Search Reports"
                            className="pl-10 w-full md:w-42 lg:w-64 py-5"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <div className="flex items-center gap-2">
                        <Select
                            name="Report Status"
                            value={statusFilter} onValueChange={setStatusFilter}
                            aria-label="Filter by report status"
                        >
                            <SelectTrigger className="w-full md:w-36 lg:w-48 py-5 text-sm font-normal text-[#1E4841] border shadow-xs">
                                <SelectValue placeholder="All Statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                {hasActiveFilters && (
                                    <Button
                                        aria-label="Clear Filters"
                                        variant="outline"
                                        size="default"
                                        className="py-5 text-sm font-normal text-[#1E4841]"
                                        onClick={clearFilters}
                                    >
                                        <X className="w-4 h-4 mr-1" />
                                        Clear
                                    </Button>
                                )}
                                <SelectItem value="all">All Statuses</SelectItem>
                                {uniqueStatuses.map(status => (
                                    <SelectItem key={status} value={status}>{status}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Button
                            aria-label="Filter Reports"
                            variant="outline"
                            size="default"
                            className="py-5 text-sm font-normal text-[#1E4841] flex w-fit">
                            <ListFilter />
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="w-full p-6">
                {isLoading ? (
                    <div className="space-y-4">
                        {[...Array(5)].map((_, index) => (
                            <div key={index} className="flex items-center gap-4 p-4 border rounded-lg animate-pulse">
                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                                <div className="h-4 bg-gray-200 rounded flex-1"></div>
                                <div className="h-4 bg-gray-200 rounded w-24"></div>
                                <div className="h-4 bg-gray-200 rounded w-16"></div>
                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <DataTable columns={columns} data={filteredReports} />
                )}
            </CardContent>
        </Card>
    );
}