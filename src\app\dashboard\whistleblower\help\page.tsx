"use client";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import Header from "@/components/dashboard-components/Header";
import { BookOpen, Download, FileText, HelpCircle, Home, Lock, MessageCircle, Search, Shield, Video } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { helpFaqData, helpCategories, quickLinks, popularHelpTopics } from "@/lib/mockData/contentData";
import { useState } from "react";

export default function HelpPage() {
    const [activeFilter, setActiveFilter] = useState("All");

    const getFaqData = () => {
        switch (activeFilter) {
            case "General":
                return helpFaqData.general.slice(0, 5);
            case "Reporting":
                return helpFaqData.reporting.slice(0, 5);
            case "Security":
                return helpFaqData.security.slice(0, 5);
            default:
                return [
                    ...helpFaqData.general.slice(0, 2),
                    ...helpFaqData.reporting.slice(0, 2),
                    ...helpFaqData.security.slice(0, 1)
                ];
        }
    };

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main id="main-content" className="bg-gray-50 min-h-screen" aria-label="Help and FAQ">
                    <header className="px-6 py-4 w-full flex justify-between items-center bg-white">
                        <div>
                            <Breadcrumb>
                                <BreadcrumbList>
                                    <BreadcrumbItem>
                                        <BreadcrumbLink href="/dashboard" className="flex items-center gap-1">
                                            <Home className="w-4 h-4" />
                                            Dashboard
                                        </BreadcrumbLink>
                                    </BreadcrumbItem>
                                    <BreadcrumbSeparator />
                                    <BreadcrumbItem>
                                        <BreadcrumbPage>Help & FAQ</BreadcrumbPage>
                                    </BreadcrumbItem>
                                </BreadcrumbList>
                            </Breadcrumb>
                            <h1 className="text-3xl font-bold text-[#1F2937]">Help & FAQ</h1>
                            <p className="text-[#6B7280] mt-2">Find answers to common questions and learn how to use the 7IRIS platform</p>
                        </div>
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <Input className="pl-10 w-80" placeholder="Search help topics..." />
                        </div>
                    </header>
                    <section className="p-6 w-full flex gap-6">
                        <div className="bg-white rounded-lg shadow-md p-6 w-1/4">
                            <h2 className="text-lg font-semibold text-[#1F2937] mb-4">Help Categories</h2>
                            <div className="space-y-2">
                                {helpCategories.map((category, index) => {
                                    const IconComponent = category.icon === "HelpCircle" ? HelpCircle :
                                                        category.icon === "BookOpen" ? BookOpen :
                                                        category.icon === "Shield" ? Shield :
                                                        MessageCircle;
                                    return (
                                        <Button
                                            key={index}
                                            variant="ghost"
                                            size="sm"
                                            className="w-full justify-start text-left hover:bg-gray-50 p-3"
                                            aria-label={category.title}
                                        >
                                            <IconComponent className="w-4 h-4 mr-3 text-gray-600" />
                                            <span className="text-sm text-gray-700">{category.title}</span>
                                        </Button>
                                    );
                                })}
                            </div>

                            <h2 className="text-lg font-semibold text-[#1F2937] mb-4 mt-8">Quick Links</h2>
                            <div className="space-y-2">
                                {quickLinks.map((link, index) => {
                                    const IconComponent = link.icon === "FileText" ? FileText :
                                                        link.icon === "Video" ? Video :
                                                        link.icon === "BookOpen" ? BookOpen :
                                                        Download;
                                    return (
                                        <Button
                                            key={index}
                                            variant="ghost"
                                            size="sm"
                                            className="w-full justify-start text-left hover:bg-gray-50 p-3"
                                            aria-label={link.title}
                                        >
                                            <IconComponent className="w-4 h-4 mr-3 text-gray-600" />
                                            <span className="text-sm text-gray-700">{link.title}</span>
                                        </Button>
                                    );
                                })}
                            </div>
                        </div>
                        <div className="bg-white rounded-lg shadow-md p-6 w-3/4">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-bold text-[#1F2937]">Frequently Asked Questions</h2>
                                <div className="flex gap-2">
                                    {["All", "General", "Reporting", "Security"].map((filter) => (
                                        <Button
                                            key={filter}
                                            variant={activeFilter === filter ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => setActiveFilter(filter)}
                                            className={activeFilter === filter ?
                                                "bg-[#1E4841] text-white hover:bg-[#1E4841]/90" :
                                                "text-gray-600 hover:bg-gray-50"
                                            }
                                        >
                                            {filter}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                            <div className="mb-6">
                                <Accordion type="single" collapsible className="w-full">
                                    {getFaqData().map((faq) => (
                                        <AccordionItem key={faq.id} value={`item-${faq.id}`}>
                                            <AccordionTrigger className="text-left font-medium text-gray-900 hover:text-[#1E4841]">
                                                {faq.question}
                                            </AccordionTrigger>
                                            <AccordionContent className="text-gray-600 leading-relaxed">
                                                {faq.answer}
                                            </AccordionContent>
                                        </AccordionItem>
                                    ))}
                                </Accordion>
                            </div>
                            <div className="flex justify-center">
                                <Button
                                    variant="outline"
                                    className="px-8 py-3 text-[#1E4841] border-[#1E4841] hover:bg-[#1E4841] hover:text-white transition-all duration-300 font-medium"
                                    aria-label="View all FAQs"
                                >
                                    View All FAQs
                                </Button>
                            </div>
                        </div>
                    </section>
                    <section className="bg-white p-6">
                        <div className="max-w-6xl mx-auto">
                            <h2 className="text-2xl font-bold text-[#1F2937] text-center mb-8">Popular Help Topics</h2>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                                {popularHelpTopics.map((topic, index) => {
                                    const IconComponent = topic.icon === "FileText" ? FileText :
                                                        topic.icon === "Shield" ? Shield :
                                                        topic.icon === "MessageSquare" ? MessageCircle :
                                                        Lock;
                                    return (
                                        <div key={index} className="flex flex-col items-center text-center p-6 rounded-lg border border-gray-200 hover:border-[#1E4841] hover:shadow-md transition-all duration-300 cursor-pointer group">
                                            <div className="w-16 h-16 bg-[#F0FDF4] rounded-full flex items-center justify-center mb-4 group-hover:bg-[#1E4841] transition-colors duration-300">
                                                <IconComponent className="w-8 h-8 text-[#1E4841] group-hover:text-white transition-colors duration-300" />
                                            </div>
                                            <h3 className="font-semibold text-gray-900 mb-2">{topic.title}</h3>
                                            <p className="text-sm text-gray-600 leading-relaxed">{topic.description}</p>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </>
    )
}