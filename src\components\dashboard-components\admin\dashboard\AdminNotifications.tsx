"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, AlertTriangle, Info, CheckCircle, Clock } from "lucide-react";
import { adminNotifications } from "@/lib/mockData";

const getNotificationIcon = (type: string) => {
    switch (type) {
        case 'warning':
            return AlertTriangle;
        case 'urgent':
            return AlertTriangle;
        case 'info':
            return Info;
        case 'success':
            return CheckCircle;
        default:
            return Bell;
    }
};

const getNotificationColor = (type: string) => {
    switch (type) {
        case 'warning':
            return 'text-yellow-600 bg-yellow-50';
        case 'urgent':
            return 'text-red-600 bg-red-50';
        case 'info':
            return 'text-blue-600 bg-blue-50';
        case 'success':
            return 'text-green-600 bg-green-50';
        default:
            return 'text-gray-600 bg-gray-50';
    }
};

export default function AdminNotifications() {
    return (
        <Card className="bg-white border-0 shadow-sm">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <CardTitle className="text-lg font-semibold text-gray-900">
                            Notifications
                        </CardTitle>
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                            {adminNotifications.unread}
                        </Badge>
                    </div>
                    <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                        View All
                    </Button>
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-3">
                    {adminNotifications.items.map((notification) => {
                        const Icon = getNotificationIcon(notification.type);
                        const colorClasses = getNotificationColor(notification.type);
                        
                        return (
                            <div 
                                key={notification.id} 
                                className={`flex items-start gap-3 p-3 rounded-lg border ${
                                    !notification.read ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                                }`}
                            >
                                <div className={`p-1.5 rounded-lg ${colorClasses}`}>
                                    <Icon className="h-4 w-4" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-2">
                                        <h4 className="text-sm font-medium text-gray-900">
                                            {notification.title}
                                        </h4>
                                        {!notification.read && (
                                            <div className="w-2 h-2 bg-blue-600 rounded-full" />
                                        )}
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                        {notification.message}
                                    </p>
                                    <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
                                        <Clock className="h-3 w-3" />
                                        {notification.time}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </CardContent>
        </Card>
    );
}