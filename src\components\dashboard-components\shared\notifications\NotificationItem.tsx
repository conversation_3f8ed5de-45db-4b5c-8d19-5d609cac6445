"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { Notification, NOTIFICATION_CONFIG, UI_CONSTANTS } from "@/lib";
import { getNotificationIcon } from "@/lib/utils";
import Link from "next/link";

interface NotificationItemProps {
    notification: Notification;
    onMarkAsRead: (id: string) => void;
    onDelete: (id: string) => void;
}

const getPriorityStyle = (priority: Notification['priority']) => {
    const config = NOTIFICATION_CONFIG.priorities[priority];
    return {
        className: config.color,
        style: { 
            color: priority === 'urgent' ? UI_CONSTANTS.colors.error : 
                   priority === 'high' ? UI_CONSTANTS.colors.warning :
                   priority === 'medium' ? UI_CONSTANTS.colors.info :
                   UI_CONSTANTS.colors.textSecondary
        }
    };
};

const formatTimestampExtended = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
        return 'Just now';
    } else if (diffInHours < 24) {
        return `${diffInHours}h ago`;
    } else {
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays === 1) return 'Yesterday';
        if (diffInDays < 7) return `${diffInDays} days ago`;
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
};

export default function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
    const Icon = getNotificationIcon(notification.type);
    const priorityStyle = getPriorityStyle(notification.priority);
    const notificationId = notification._id || (notification as Notification & { id?: string }).id || `temp-${Math.random()}`;

    return (
        <div
            className={`p-3 sm:p-4 md:p-6 hover:bg-gray-50 transition-colors ${
                notification.status === 'unread' ? 'bg-green-50/30 border-l-4 border-l-green-500' : ''
            }`}
        >
            <div className="flex items-start gap-2 sm:gap-3 md:gap-4">
                <div className={`p-1.5 sm:p-2 rounded-full flex-shrink-0 ${priorityStyle.className}`} style={priorityStyle.style}>
                    <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex flex-col gap-2 sm:gap-1">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-1 sm:gap-2">
                            <h3 className={`text-sm sm:text-base font-medium text-gray-900 leading-tight ${
                                notification.status === 'unread' ? 'font-semibold' : ''
                            }`}>
                                {notification.title}
                            </h3>
                            <div className="flex items-center gap-2 flex-shrink-0">
                                <Badge
                                    variant={notification.priority === 'urgent' ? 'destructive' : 'secondary'}
                                    className="text-xs"
                                >
                                    {notification.priority}
                                </Badge>
                                <span className="text-xs text-gray-500 whitespace-nowrap">
                                    {formatTimestampExtended(notification.createdAt ? notification.createdAt.toISOString() : (notification as Notification & { timestamp?: string }).timestamp || new Date().toISOString())}
                                </span>
                            </div>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-600 leading-relaxed pr-0 sm:pr-4">
                            {notification.message}
                        </p>
                    </div>

                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 mt-3">
                        <div className="flex flex-wrap gap-2">
                            {notification.status === 'unread' && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => onMarkAsRead(notificationId)}
                                    className="text-xs h-8 px-3 flex-1 sm:flex-none"
                                >
                                    <span className="hidden sm:inline">Mark as Read</span>
                                    <span className="sm:hidden">Read</span>
                                </Button>
                            )}
                            {notification.actionUrl && (
                                <Link href={notification.actionUrl} className="flex-1 sm:flex-none">
                                    <Button variant="outline" size="sm" className="text-xs h-8 px-3 w-full">
                                        <span className="hidden sm:inline">View Details</span>
                                        <span className="sm:hidden">Details</span>
                                    </Button>
                                </Link>
                            )}
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDelete(notificationId)}
                            className="text-xs h-8 px-3 text-red-600 hover:text-red-700 hover:bg-red-50 sm:ml-auto"
                        >
                            <Trash2 className="w-3 h-3 mr-1" />
                            Delete
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}