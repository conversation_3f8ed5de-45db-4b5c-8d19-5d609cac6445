"use client";

import { useParams } from "next/navigation";
import { useState, useMemo } from "react";
import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import { CategoryBlogCard } from "@/components/home-components/blog/BlogCard";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext
} from "@/components/ui/pagination";

// Import blog data from shared file
import { ALL_BLOG_POSTS } from "@/lib/mockData/blogData";

export default function CategoryPage() {
  const params = useParams();
  const categoryName = typeof params.name === 'string' ? params.name : '';
  const decodedCategory = decodeURIComponent(categoryName);
  
  const [page, setPage] = useState(1);
  const pageSize = 6;

  // Filter blog cards by category
  const filteredCards = useMemo(() => {
    const cards = ALL_BLOG_POSTS.filter(card => 
      card.category.toLowerCase() === decodedCategory.toLowerCase()
    );
    
    // Pagination
    const start = (page - 1) * pageSize;
    return cards.slice(start, start + pageSize);
  }, [decodedCategory, page]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    const filteredCount = ALL_BLOG_POSTS.filter(card => 
      card.category.toLowerCase() === decodedCategory.toLowerCase()
    ).length;
    
    return Math.max(1, Math.ceil(filteredCount / pageSize));
  }, [decodedCategory]);

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 pt-20">
        <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px] py-6 sm:py-8 md:py-12">
          <Link href="/blog" className="inline-flex items-center text-[#1E4841] mb-4 sm:mb-6 md:mb-8 hover:underline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to All Articles
          </Link>
          
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1E4841] mb-6 sm:mb-8">
            {decodedCategory}
          </h1>
          
          {filteredCards.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {filteredCards.map((card, index) => (
                  <CategoryBlogCard key={index} card={card} />
                ))}
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <Pagination className="mt-8 sm:mt-10 md:mt-12">
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        className="bg-[#ECF4E9] text-[#1E4841] font-semibold disabled:opacity-50"
                        onClick={() => page > 1 && setPage(page - 1)}
                        aria-disabled={page === 1}
                        tabIndex={page === 1 ? -1 : 0}
                      />
                    </PaginationItem>
                    {[...Array(totalPages)].map((_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={page === i + 1}
                          className={`${page === i + 1 ? 'bg-[#1E4841] text-white' : 'bg-white text-[#1E4841] border border-[#ECF4E9]'} w-10 h-10`}
                          onClick={() => setPage(i + 1)}
                        >
                          <span>{i + 1}</span>
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        className="bg-[#ECF4E9] text-[#1E4841] font-semibold disabled:opacity-50"
                        onClick={() => page < totalPages && setPage(page + 1)}
                        aria-disabled={page === totalPages} 
                        tabIndex={page === totalPages ? -1 : 0}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              )}
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 sm:py-12 md:py-16">
              <p className="text-lg sm:text-xl text-[#1E4841] font-semibold mb-2">No articles found in this category</p>
              <p className="text-gray-500 mb-8">Try browsing other categories</p>
              <Link href="/blog">
                <Button variant="default" className="bg-[#1E4841] text-white hover:bg-[#132f2a]">
                  View All Articles
                </Button>
              </Link>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}