"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { Shield, Upload, Home, Calendar, Clock, MapPin, Users, FileText, AlertTriangle } from "lucide-react";
import Link from "next/link";

interface DetailedReportData {
  // Basic info from step 1
  title: string;
  category: string;
  location: string;
  description: string;
  isAnonymous: boolean;
  
  // Step 2 detailed information
  incidentDate: string;
  incidentTime: string;
  specificLocation: string;
  departmentInvolved: string;
  witnessInfo: {
    hasWitnesses: boolean;
    witnessDetails: string;
  };
  evidenceInfo: {
    hasEvidence: boolean;
    evidenceDescription: string;
    files: File[];
  };
  previouslyReported: {
    hasBeenReported: boolean;
    reportedTo: string;
    reportedWhen: string;
    outcome: string;
  };
  impactAssessment: {
    financialImpact: string;
    reputationalImpact: string;
    operationalImpact: string;
    legalImpact: string;
  };
  urgencyLevel: string;
  additionalComments: string;
  followUpPreference: string;
}

export default function NewReportStep2Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<DetailedReportData>({
    title: "",
    category: "",
    location: "",
    description: "",
    isAnonymous: false,
    incidentDate: "",
    incidentTime: "",
    specificLocation: "",
    departmentInvolved: "",
    witnessInfo: {
      hasWitnesses: false,
      witnessDetails: ""
    },
    evidenceInfo: {
      hasEvidence: false,
      evidenceDescription: "",
      files: []
    },
    previouslyReported: {
      hasBeenReported: false,
      reportedTo: "",
      reportedWhen: "",
      outcome: ""
    },
    impactAssessment: {
      financialImpact: "",
      reputationalImpact: "",
      operationalImpact: "",
      legalImpact: ""
    },
    urgencyLevel: "Medium",
    additionalComments: "",
    followUpPreference: "Email"
  });

  // Load data from step 1 if available
  useEffect(() => {
    const step1Data = sessionStorage.getItem('reportStep1Data');
    if (step1Data) {
      const parsedData = JSON.parse(step1Data);
      setFormData(prev => ({
        ...prev,
        ...parsedData
      }));
    }
  }, []);

  const departments = [
    "Human Resources",
    "Finance",
    "Operations",
    "Sales",
    "Marketing",
    "IT",
    "Legal",
    "Compliance",
    "Other"
  ];

  const urgencyLevels = [
    "Low",
    "Medium", 
    "High",
    "Critical"
  ];

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent as keyof DetailedReportData]: {
          ...(prev[parent as keyof DetailedReportData] as any),
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        evidenceInfo: {
          ...prev.evidenceInfo,
          files: [...prev.evidenceInfo.files, ...fileArray]
        }
      }));
    }
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      evidenceInfo: {
        ...prev.evidenceInfo,
        files: prev.evidenceInfo.files.filter((_, i) => i !== index)
      }
    }));
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/reports/draft-detailed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success('Detailed draft saved successfully');
        router.push('/dashboard/whistleblower/my-reports');
      } else {
        toast.error(result.error || 'Failed to save draft');
      }
    } catch (error) {
      console.error('Save draft error:', error);
      toast.error('Failed to save draft');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/reports/submit-detailed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          status: 'New',
          priority: formData.urgencyLevel
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success('Report submitted successfully');
        sessionStorage.removeItem('reportStep1Data');
        router.push('/dashboard/whistleblower/my-reports');
      } else {
        toast.error(result.error || 'Failed to submit report');
      }
    } catch (error) {
      console.error('Submit error:', error);
      toast.error('Failed to submit report');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    // Save current data to session storage
    sessionStorage.setItem('reportStep2Data', JSON.stringify(formData));
    router.push('/dashboard/whistleblower/new-report');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower">
                  <Home className="w-4 h-4" />
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower/my-reports">
                  My Reports
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>New Report - Step 2</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              New Whistleblower Report - Step 2
            </h1>
            <p className="text-gray-600">
              Provide additional details to help us investigate your concern
            </p>
          </div>

          <Alert className="mb-6 border-green-200 bg-green-50">
            <Shield className="w-4 h-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <strong>Step 2 of 2:</strong> Please provide as much detail as possible. All information remains confidential and will help us conduct a thorough investigation.
            </AlertDescription>
          </Alert>

          {/* Incident Details */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Incident Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="incidentDate">Date of Incident</Label>
                  <Input
                    id="incidentDate"
                    type="date"
                    value={formData.incidentDate}
                    onChange={(e) => handleInputChange('incidentDate', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="incidentTime">Time of Incident</Label>
                  <Input
                    id="incidentTime"
                    type="time"
                    value={formData.incidentTime}
                    onChange={(e) => handleInputChange('incidentTime', e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="specificLocation">Specific Location</Label>
                <Input
                  id="specificLocation"
                  placeholder="Building, floor, room number, or specific area"
                  value={formData.specificLocation}
                  onChange={(e) => handleInputChange('specificLocation', e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="departmentInvolved">Department/Team Involved</Label>
                <Select
                  value={formData.departmentInvolved}
                  onValueChange={(value) => handleInputChange('departmentInvolved', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Witness Information */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Witness Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasWitnesses"
                  checked={formData.witnessInfo.hasWitnesses}
                  onCheckedChange={(checked) => 
                    handleInputChange('witnessInfo.hasWitnesses', checked as boolean)
                  }
                />
                <Label htmlFor="hasWitnesses">
                  There were witnesses to this incident
                </Label>
              </div>

              {formData.witnessInfo.hasWitnesses && (
                <div>
                  <Label htmlFor="witnessDetails">Witness Details</Label>
                  <Textarea
                    id="witnessDetails"
                    placeholder="Please provide names, contact information, or descriptions of witnesses (if known and comfortable sharing)"
                    value={formData.witnessInfo.witnessDetails}
                    onChange={(e) => handleInputChange('witnessInfo.witnessDetails', e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Evidence and Documentation */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Evidence and Documentation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasEvidence"
                  checked={formData.evidenceInfo.hasEvidence}
                  onCheckedChange={(checked) => 
                    handleInputChange('evidenceInfo.hasEvidence', checked as boolean)
                  }
                />
                <Label htmlFor="hasEvidence">
                  I have evidence or documentation related to this incident
                </Label>
              </div>

              {formData.evidenceInfo.hasEvidence && (
                <>
                  <div>
                    <Label htmlFor="evidenceDescription">Evidence Description</Label>
                    <Textarea
                      id="evidenceDescription"
                      placeholder="Describe the evidence you have (emails, documents, photos, recordings, etc.)"
                      value={formData.evidenceInfo.evidenceDescription}
                      onChange={(e) => handleInputChange('evidenceInfo.evidenceDescription', e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="fileUpload">Upload Files</Label>
                    <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600 mb-2">
                        Drag and drop files here, or click to select
                      </p>
                      <input
                        id="fileUpload"
                        type="file"
                        multiple
                        onChange={(e) => handleFileUpload(e.target.files)}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('fileUpload')?.click()}
                      >
                        Choose Files
                      </Button>
                    </div>
                    
                    {formData.evidenceInfo.files.length > 0 && (
                      <div className="mt-2 space-y-2">
                        {formData.evidenceInfo.files.map((file, index) => (
                          <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                            <span className="text-sm">{file.name}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Previous Reporting */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Previous Reporting</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasBeenReported"
                  checked={formData.previouslyReported.hasBeenReported}
                  onCheckedChange={(checked) => 
                    handleInputChange('previouslyReported.hasBeenReported', checked as boolean)
                  }
                />
                <Label htmlFor="hasBeenReported">
                  This incident has been reported before
                </Label>
              </div>

              {formData.previouslyReported.hasBeenReported && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="reportedTo">Reported To</Label>
                    <Input
                      id="reportedTo"
                      placeholder="Who was this reported to? (supervisor, HR, etc.)"
                      value={formData.previouslyReported.reportedTo}
                      onChange={(e) => handleInputChange('previouslyReported.reportedTo', e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="reportedWhen">When was it reported?</Label>
                    <Input
                      id="reportedWhen"
                      type="date"
                      value={formData.previouslyReported.reportedWhen}
                      onChange={(e) => handleInputChange('previouslyReported.reportedWhen', e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="outcome">Outcome</Label>
                    <Textarea
                      id="outcome"
                      placeholder="What was the outcome or response to the previous report?"
                      value={formData.previouslyReported.outcome}
                      onChange={(e) => handleInputChange('previouslyReported.outcome', e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Impact Assessment */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Impact Assessment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="financialImpact">Financial Impact</Label>
                  <Select
                    value={formData.impactAssessment.financialImpact}
                    onValueChange={(value) => handleInputChange('impactAssessment.financialImpact', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select impact level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="None">None</SelectItem>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="reputationalImpact">Reputational Impact</Label>
                  <Select
                    value={formData.impactAssessment.reputationalImpact}
                    onValueChange={(value) => handleInputChange('impactAssessment.reputationalImpact', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select impact level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="None">None</SelectItem>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="operationalImpact">Operational Impact</Label>
                  <Select
                    value={formData.impactAssessment.operationalImpact}
                    onValueChange={(value) => handleInputChange('impactAssessment.operationalImpact', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select impact level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="None">None</SelectItem>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="legalImpact">Legal/Compliance Impact</Label>
                  <Select
                    value={formData.impactAssessment.legalImpact}
                    onValueChange={(value) => handleInputChange('impactAssessment.legalImpact', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select impact level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="None">None</SelectItem>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Urgency and Follow-up */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Urgency and Follow-up Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="urgencyLevel">Urgency Level</Label>
                <Select
                  value={formData.urgencyLevel}
                  onValueChange={(value) => handleInputChange('urgencyLevel', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {urgencyLevels.map((level) => (
                      <SelectItem key={level} value={level}>
                        {level}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="followUpPreference">Preferred Follow-up Method</Label>
                <Select
                  value={formData.followUpPreference}
                  onValueChange={(value) => handleInputChange('followUpPreference', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Email">Email</SelectItem>
                    <SelectItem value="Phone">Phone</SelectItem>
                    <SelectItem value="In-person">In-person Meeting</SelectItem>
                    <SelectItem value="Secure Message">Secure Message</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="additionalComments">Additional Comments</Label>
                <Textarea
                  id="additionalComments"
                  placeholder="Any additional information or comments you'd like to provide"
                  value={formData.additionalComments}
                  onChange={(e) => handleInputChange('additionalComments', e.target.value)}
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <Button
              variant="outline"
              onClick={goBack}
              disabled={loading}
            >
              Back to Step 1
            </Button>
            
            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={loading}
              >
                Save as Draft
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={loading}
                className="bg-[#1E4841] hover:bg-[#2A5D54]"
              >
                Submit Report
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}