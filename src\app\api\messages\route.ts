import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');
    
    if (!conversationId) {
      return NextResponse.json(
        { success: false, error: 'conversationId is required' },
        { status: 400 }
      );
    }
    
    // Verify user has access to this conversation
    const conversation = await DataService.getConversationById(conversationId);
    if (!conversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found' },
        { status: 404 }
      );
    }
    
    // Check if user is a participant in the conversation
    const isParticipant = (conversation as unknown as { participants: Array<{ _id: { toString(): string } }> }).participants.some(
      (participant: { _id: { toString(): string } }) => participant._id.toString() === request.user!.id
    );
    
    if (!isParticipant) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this conversation' },
        { status: 403 }
      );
    }
    
    const messages = await DataService.getMessages(conversationId);
    
    return NextResponse.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('Messages GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const messageData = await request.json();
    
    if (!messageData.conversationId || !messageData.content) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Verify user has access to this conversation
    const conversation = await DataService.getConversationById(messageData.conversationId);
    if (!conversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found' },
        { status: 404 }
      );
    }
    
    // Check if user is a participant in the conversation
    const isParticipant = (conversation as unknown as { participants: Array<{ _id: { toString(): string } }> }).participants.some(
      (participant: { _id: { toString(): string } }) => participant._id.toString() === request.user!.id
    );
    
    if (!isParticipant) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this conversation' },
        { status: 403 }
      );
    }
    
    // Ensure senderId matches authenticated user
    const message = await DataService.createMessage({
      ...messageData,
      senderId: request.user!.id
    });
    
    return NextResponse.json({
      success: true,
      data: message
    }, { status: 201 });
  } catch (error) {
    console.error('Messages POST API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export async function PUT(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');
    const action = searchParams.get('action');
    
    if (!messageId) {
      return NextResponse.json(
        { success: false, error: 'messageId is required' },
        { status: 400 }
      );
    }
    
    const requestData = await request.json();
    
    switch (action) {
      case 'mark_read':
        if (!requestData.userId) {
          return NextResponse.json(
            { success: false, error: 'userId is required' },
            { status: 400 }
          );
        }
        const readMessage = await DataService.markMessageAsRead(messageId, requestData.userId);
        return NextResponse.json({
          success: true,
          data: readMessage
        });
        
      case 'update':
        const updatedMessage = await DataService.updateMessage(messageId, {
          content: requestData.content,
          htmlContent: requestData.htmlContent
        });
        return NextResponse.json({
          success: true,
          data: updatedMessage
        });
        
      case 'add_reaction':
        if (!requestData.userId || !requestData.emoji) {
          return NextResponse.json(
            { success: false, error: 'userId and emoji are required' },
            { status: 400 }
          );
        }
        const messageWithReaction = await DataService.addReaction(
          messageId, 
          requestData.userId, 
          requestData.emoji
        );
        return NextResponse.json({
          success: true,
          data: messageWithReaction
        });
        
      case 'remove_reaction':
        if (!requestData.userId) {
          return NextResponse.json(
            { success: false, error: 'userId is required' },
            { status: 400 }
          );
        }
        const messageWithoutReaction = await DataService.removeReaction(messageId, requestData.userId);
        return NextResponse.json({
          success: true,
          data: messageWithoutReaction
        });
        
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Messages PUT API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');
    const userId = searchParams.get('userId');
    
    if (!messageId || !userId) {
      return NextResponse.json(
        { success: false, error: 'messageId and userId are required' },
        { status: 400 }
      );
    }
    
    const deletedMessage = await DataService.deleteMessage(messageId, userId);
    
    return NextResponse.json({
      success: true,
      data: deletedMessage
    });
  } catch (error) {
    console.error('Messages DELETE API error:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Internal server error' },
      { status: error instanceof Error && error.message.includes('Unauthorized') ? 403 : 500 }
    );
  }
}