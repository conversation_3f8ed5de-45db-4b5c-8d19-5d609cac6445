@echo off
echo Starting 7IRIS Whistleblower Platform Development Environment
echo.
echo Starting WebSocket Server on port 3001...
start "WebSocket Server" cmd /k "node src/lib/websocket/devServer.js"

echo Waiting for WebSocket server to start...
timeout /t 3 /nobreak > nul

echo Starting Main Application on port 3000...
start "Main App" cmd /k "node server.js"

echo.
echo Development environment started!
echo.
echo Main Application: http://localhost:3000
echo WebSocket Server: ws://localhost:3001/ws
echo.
echo Real-time Demo Pages:
echo - Whistleblower: http://localhost:3000/dashboard/whistleblower/real-time-demo
echo - Admin: http://localhost:3000/dashboard/admin/real-time-demo
echo.
echo Press any key to close this window...
pause > nul