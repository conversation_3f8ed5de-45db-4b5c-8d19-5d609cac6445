import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const reportData = await request.json();
    
    // Create draft report
    const draftReport = await DataService.createReport({
      ...reportData,
      userId: request.user!.id,
      status: 'Draft',
      priority: 'Medium',
      isDraft: true
    });
    
    return NextResponse.json({
      success: true,
      data: draftReport
    }, { status: 201 });
  } catch (error) {
    console.error('Draft report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save draft' },
      { status: 500 }
    );
  }
});