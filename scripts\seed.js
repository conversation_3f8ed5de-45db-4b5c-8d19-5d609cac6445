const { execSync } = require('child_process');
const path = require('path');

// Set NODE_ENV to development for seeding
process.env.NODE_ENV = 'development';

try {
  console.log('🌱 Starting database seeding process...');
  
  // Run the TypeScript seeding script
  const seedScript = path.join(__dirname, 'seedDatabase.ts');
  execSync(`npx tsx "${seedScript}"`, { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('✅ Database seeding completed successfully!');
} catch (error) {
  console.error('❌ Database seeding failed:', error.message);
  process.exit(1);
}