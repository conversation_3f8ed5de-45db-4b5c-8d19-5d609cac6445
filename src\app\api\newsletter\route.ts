import { NextRequest, NextResponse } from 'next/server';
import { User } from '@/lib/db/models';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }
    
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: 'Already subscribed'
      });
    }
    
    await User.create({ 
      email,
      role: 'whistleblower',
      isActive: true
    });
    
    return NextResponse.json({
      success: true,
      message: 'Subscribed successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Newsletter API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}