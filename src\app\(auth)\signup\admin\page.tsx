"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Eye, EyeOff, Mail, User, Building2, Phone, LockKeyhole, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Form validation schema
const formSchema = z.object({
  fullName: z.string().min(2, 'Name must be at least 2 characters'),
  companyName: z.string().min(2, 'Company name is required'),
  email: z.email('Please enter a valid email address'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms and privacy policy"
  })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export default function AdminSignupPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      companyName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      setSubmitStatus({ type: null, message: '' });

      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          role: 'admin'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSubmitStatus({
          type: 'success',
          message: 'Admin account created successfully! You can now log in with your credentials.'
        });
        
        // Reset form
        form.reset();
        
        // Redirect to login page after 2 seconds
        setTimeout(() => {
          router.push('/login/admin');
        }, 2000);
      } else {
        setSubmitStatus({
          type: 'error',
          message: result.error || 'Failed to create account. Please try again.'
        });
      }
    } catch (error) {
      console.error('Signup error:', error);
      setSubmitStatus({
        type: 'error',
        message: 'Network error. Please check your connection and try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left side form */}
      <div className="w-full lg:w-1/2 xl:w-1/2 p-4 sm:p-6 md:p-8 lg:p-12 xl:p-16 flex flex-col justify-center items-center bg-white">
        <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
          <div className="mb-6 sm:mb-8">
            <Link href="/">
              <Image src="/logo.svg" alt="Logo" width={120} height={40} className="h-auto w-16 sm:w-20 lg:w-24" />
            </Link>
          </div>

          {submitStatus.type && (
            <Alert className={`mb-4 sm:mb-6 ${submitStatus.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              {submitStatus.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={`text-xs sm:text-sm ${submitStatus.type === 'success' ? 'text-green-800' : 'text-red-800'}`}>
                {submitStatus.message}
              </AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3 sm:space-y-4">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Full Name</FormLabel>
                    <div className="relative">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                        <User className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                      </span>
                      <FormControl>
                        <Input
                          {...field}
                          className="pl-9 sm:pl-10 h-10 sm:h-11 text-sm sm:text-base"
                          placeholder="Enter your name"
                          disabled={isLoading}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className="text-xs sm:text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Company Name</FormLabel>
                    <div className="relative">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                        <Building2 className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                      </span>
                      <FormControl>
                        <Input
                          {...field}
                          className="pl-9 sm:pl-10 h-10 sm:h-11 text-sm sm:text-base"
                          placeholder="Enter your company name"
                          disabled={isLoading}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className="text-xs sm:text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Email Address</FormLabel>
                    <div className="relative">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                        <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                      </span>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          className="pl-9 sm:pl-10 h-10 sm:h-11 text-sm sm:text-base"
                          placeholder="Enter your email address"
                          disabled={isLoading}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className="text-xs sm:text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Phone Number</FormLabel>
                    <div className="relative">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                        <Phone className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                      </span>
                      <FormControl>
                        <Input
                          {...field}
                          type="tel"
                          className="pl-9 sm:pl-10 h-10 sm:h-11 text-sm sm:text-base"
                          placeholder="Enter your phone number"
                          disabled={isLoading}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className="text-xs sm:text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Password</FormLabel>
                    <div className="relative">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                        <LockKeyhole className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                      </span>
                      <FormControl>
                        <Input
                          {...field}
                          type={showPassword ? "text" : "password"}
                          className="pl-9 sm:pl-10 pr-9 sm:pr-10 h-10 sm:h-11 text-sm sm:text-base"
                          placeholder="Create a password"
                          disabled={isLoading}
                        />
                      </FormControl>
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    <FormMessage className="text-xs sm:text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Confirm Password</FormLabel>
                    <div className="relative">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                        <LockKeyhole className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                      </span>
                      <FormControl>
                        <Input
                          {...field}
                          type={showConfirmPassword ? "text" : "password"}
                          className="pl-9 sm:pl-10 pr-9 sm:pr-10 h-10 sm:h-11 text-sm sm:text-base"
                          placeholder="Confirm your password"
                          disabled={isLoading}
                        />
                      </FormControl>
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    <FormMessage className="text-xs sm:text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="agreeToTerms"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="mt-1"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                        I agree to the{' '}
                        <Link href="/terms" className="text-[#1E4841] hover:underline font-medium">
                          Terms of service
                        </Link>
                        {' '}and{' '}
                        <Link href="/privacy" className="text-[#1E4841] hover:underline font-medium">
                          Privacy policy
                        </Link>
                      </FormLabel>
                      <FormMessage className="text-xs sm:text-sm" />
                    </div>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white h-10 sm:h-12 text-sm sm:text-base font-semibold"
                disabled={isLoading}
              >
                {isLoading ? "Creating account..." : "Create Account"}
              </Button>

              <p className="text-center text-xs sm:text-sm text-gray-600 mt-3 sm:mt-4">
                Already have an account?{' '}
                <Link href="/login/admin" className="text-[#1E4841] hover:underline font-medium">
                  Login here
                </Link>
              </p>
            </form>
          </Form>
        </div>
      </div>

      {/* Right side content */}
      <div className="hidden lg:block lg:w-1/2 xl:w-1/2 bg-[#F9FAFB] p-4 sm:p-6 lg:p-8 xl:p-12 flex items-center justify-center">
        <div className="max-w-md lg:max-w-lg xl:max-w-xl space-y-4 sm:space-y-6">
          <div className="space-y-2">
            <div className="flex items-center space-x-2 sm:space-x-4">
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-[#1E4841] text-white flex items-center justify-center text-sm sm:text-base font-semibold">
                1
              </div>
              <div className="h-0.5 sm:h-1 flex-1 bg-gray-200">
                <div className="h-full w-full bg-[#1E4841]" />
              </div>
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm sm:text-base font-semibold">
                2
              </div>
              <div className="h-0.5 sm:h-1 flex-1 bg-gray-200" />
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm sm:text-base font-semibold">
                3
              </div>
            </div>
            <div className="flex justify-between text-xs sm:text-sm text-gray-600">
              <span>Admin Signup</span>
              <span className="hidden sm:inline">Subscription Plan</span>
              <span className="sm:hidden">Plan</span>
              <span>Completion</span>
            </div>
          </div>

          <div className="space-y-3 sm:space-y-4">
            <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-[#1E4841]">Create Admin Account</h2>
            <p className="text-gray-600 text-sm sm:text-base">
              Set up your organization to start managing whistleblower cases securely.
            </p>
          </div>

          <div className="relative aspect-video rounded-lg overflow-hidden">
            <Image
              src="/(auth)/signup/step1.jpg"
              alt="Admin sign up"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
