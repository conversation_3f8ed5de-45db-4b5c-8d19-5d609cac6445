"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Eye, EyeOff, Mail, User, Phone, LockKeyhole, CheckCircle, AlertCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ReCaptcha {
    render: (container: string | HTMLElement, options: {
        sitekey: string;
        theme?: 'light' | 'dark';
        callback?: (token: string) => void;
        'expired-callback'?: () => void;
    }) => number;
    reset: (widgetId?: number) => void;
}

declare global {
    interface Window {
        grecaptcha: ReCaptcha;
        onRecaptchaLoad: () => void;
    }
}

const formSchema = z.object({
    fullName: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.email('Please enter a valid email address'),
    phoneNumber: z.string().optional(),
    password: z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[0-9]/, 'Password must contain at least one number')
        .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
    confirmPassword: z.string(),
    agreeToTerms: z.boolean().refine((val) => val === true, {
        message: "You must agree to the terms and privacy policy"
    }),
    recaptchaToken: z.string().min(1, 'Please complete the reCAPTCHA verification')
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});

export default function WhistleblowerSignupPage() {
    const router = useRouter();
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [recaptchaError, setRecaptchaError] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<{
        type: 'success' | 'error' | null;
        message: string;
    }>({ type: null, message: '' });

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            fullName: "",
            email: "",
            phoneNumber: "",
            password: "",
            confirmPassword: "",
            agreeToTerms: false,
            recaptchaToken: ""
        },
    });

    useEffect(() => {
        // Load reCAPTCHA script
        const script = document.createElement('script');
        script.src = `https://www.google.com/recaptcha/api.js?render=explicit`;
        script.async = true;
        document.body.appendChild(script);

        const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
        if (!siteKey) {
            console.error('reCAPTCHA site key is not configured');
            return;
        }

        window.onRecaptchaLoad = () => {
            window.grecaptcha.render('recaptcha', {
                sitekey: siteKey,
                theme: 'light',
                callback: (token: string) => {
                    form.setValue('recaptchaToken', token);
                    setRecaptchaError("");
                },
                'expired-callback': () => {
                    form.setValue('recaptchaToken', '');
                    setRecaptchaError("reCAPTCHA verification expired. Please verify again.");
                }
            });
        };

        return () => {
            document.body.removeChild(script);
            window.onRecaptchaLoad = () => { }; // Clear the callback instead of deleting
        };
    }, [form, form.setValue]);

    const togglePassword = () => {
        setShowPassword((prev) => !prev);
    };

    const toggleConfirmPassword = () => {
        setShowConfirmPassword((prev) => !prev);
    };

    const onSubmit = async (data: z.infer<typeof formSchema>) => {
        try {
            setIsLoading(true);
            setSubmitStatus({ type: null, message: '' });

            const response = await fetch('/api/auth/signup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ...data,
                    role: 'whistleblower'
                }),
            });

            const result = await response.json();

            if (result.success) {
                setSubmitStatus({
                    type: 'success',
                    message: 'Account created successfully! You can now log in with your credentials.'
                });
                
                // Reset form
                form.reset();
                
                // Reset reCAPTCHA
                if (window.grecaptcha) {
                    window.grecaptcha.reset();
                }
                
                // Redirect to login page after 2 seconds
                setTimeout(() => {
                    router.push('/login/whistleblower');
                }, 2000);
            } else {
                setSubmitStatus({
                    type: 'error',
                    message: result.error || 'Failed to create account. Please try again.'
                });
                
                // Reset reCAPTCHA on error
                if (window.grecaptcha) {
                    window.grecaptcha.reset();
                    form.setValue('recaptchaToken', '');
                }
            }
        } catch (error) {
            console.error("Signup error:", error);
            setSubmitStatus({
                type: 'error',
                message: 'Network error. Please check your connection and try again.'
            });
            
            // Reset reCAPTCHA on error
            if (window.grecaptcha) {
                window.grecaptcha.reset();
                form.setValue('recaptchaToken', '');
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center lg:h-screen">
            <div className="flex flex-col lg:flex-row w-full h-full overflow-hidden">
                {/* Left side */}
                <div className="w-full lg:w-1/2 xl:w-1/2 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-24 py-8 lg:py-12 flex flex-col justify-center">
                    <div className="flex items-center mb-6 lg:mb-8">
                        <Link href={"/"}>
                            <Image src="/logo.svg" alt="IRIS Logo" width={40} height={40} className="h-8 w-auto sm:h-10 lg:h-12 mr-3" />
                        </Link>
                    </div>
                    <h2 className="text-xl sm:text-2xl lg:text-3xl xl:text-2xl font-semibold mb-2">Create Your Secure Profile</h2>
                    <p className="text-gray-500 mb-4 sm:mb-6 text-sm sm:text-base">Start your journey in a secure, private, and compliant environment</p>

                    {submitStatus.type && (
                        <Alert className={`mb-4 sm:mb-6 ${submitStatus.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                            {submitStatus.type === 'success' ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                                <AlertCircle className="h-4 w-4 text-red-600" />
                            )}
                            <AlertDescription className={`text-xs sm:text-sm ${submitStatus.type === 'success' ? 'text-green-800' : 'text-red-800'}`}>
                                {submitStatus.message}
                            </AlertDescription>
                        </Alert>
                    )}

                    <Form {...form}>
                        <form className="space-y-3 sm:space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
                            <FormField
                                control={form.control}
                                name="fullName"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Full Name</FormLabel>
                                        <div className="relative">
                                            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                                                <User className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                                            </span>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="text"
                                                    className="pl-9 sm:pl-10 h-10 sm:h-11 text-sm sm:text-base"
                                                    placeholder="Enter your name"
                                                />
                                            </FormControl>
                                        </div>
                                        <FormMessage className="text-xs sm:text-sm" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Email Address</FormLabel>
                                        <div className="relative">
                                            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                                                <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                                            </span>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="email"
                                                    className="pl-9 sm:pl-10 h-10 sm:h-11 text-sm sm:text-base"
                                                    placeholder="Enter your email"
                                                />
                                            </FormControl>
                                        </div>
                                        <FormMessage className="text-xs sm:text-sm" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="phoneNumber"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">
                                            Phone Number (Optional)
                                            <span className="text-xs text-gray-400 ml-1 -mb-1 hidden sm:inline">- Used for account recovery or optional SMS alerts</span>
                                        </FormLabel>
                                        <div className="relative">
                                            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                                                <Phone className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                                            </span>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="tel"
                                                    className="pl-9 sm:pl-10 h-10 sm:h-11 text-sm sm:text-base"
                                                    placeholder="Enter your phone number"
                                                />
                                            </FormControl>
                                        </div>
                                        <FormMessage className="text-xs sm:text-sm" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Password</FormLabel>
                                        <div className="relative">
                                            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                                                <LockKeyhole className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                                            </span>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type={showPassword ? "text" : "password"}
                                                    className="pl-9 sm:pl-10 pr-9 sm:pr-10 h-10 sm:h-11 text-sm sm:text-base"
                                                    placeholder="Enter your password"
                                                />
                                            </FormControl>
                                            <span className="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <button
                                                    type="button"
                                                    onClick={togglePassword}
                                                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                                                >
                                                    {showPassword ? (
                                                        <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" />
                                                    ) : (
                                                        <Eye className="h-4 w-4 sm:h-5 sm:w-5" />
                                                    )}
                                                </button>
                                            </span>
                                        </div>
                                        <FormMessage className="text-xs sm:text-sm" />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="confirmPassword"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-xs sm:text-sm font-medium text-gray-700">Confirm Password</FormLabel>
                                        <div className="relative">
                                            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                                                <LockKeyhole className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                                            </span>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type={showConfirmPassword ? "text" : "password"}
                                                    className="pl-9 sm:pl-10 pr-9 sm:pr-10 h-10 sm:h-11 text-sm sm:text-base"
                                                    placeholder="Confirm your password"
                                                />
                                            </FormControl>
                                            <span className="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <button
                                                    type="button"
                                                    onClick={toggleConfirmPassword}
                                                    className="text-gray-400 hover:text-gray-600 focus:outline-none"
                                                >
                                                    {showConfirmPassword ? (
                                                        <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" />
                                                    ) : (
                                                        <Eye className="h-4 w-4 sm:h-5 sm:w-5" />
                                                    )}
                                                </button>
                                            </span>
                                        </div>
                                        <FormMessage className="text-xs sm:text-sm" />
                                    </FormItem>
                                )}
                            />

                            <div className="space-y-3 sm:space-y-4">
                                <div id="recaptcha" className="flex justify-center"></div>
                                {recaptchaError && (
                                    <p className="text-red-500 text-xs sm:text-sm text-center">{recaptchaError}</p>
                                )}
                            </div>

                            <FormField
                                control={form.control}
                                name="agreeToTerms"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                        <FormControl>
                                            <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                                className="mt-1"
                                            />
                                        </FormControl>
                                        <div className="space-y-1 leading-none">
                                            <FormLabel className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                                                I agree to the{' '}
                                                <Link href="/terms" className="text-[#1E4841] hover:underline font-medium">
                                                    Terms of service
                                                </Link>
                                                {' '}and{' '}
                                                <Link href="/privacy" className="text-[#1E4841] hover:underline font-medium">
                                                    Privacy policy
                                                </Link>
                                            </FormLabel>
                                            <FormMessage className="text-xs sm:text-sm" />
                                        </div>
                                    </FormItem>
                                )}
                            />

                            <Button
                                type="submit"
                                className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white h-10 sm:h-12 text-sm sm:text-base font-semibold"
                                disabled={isLoading}
                            >
                                {isLoading ? "Creating account..." : "Create Account"}
                            </Button>

                            <p className="text-center text-xs sm:text-sm text-gray-600 mt-3 sm:mt-4">
                                Already have an account?{' '}
                                <Link href="/login/whistleblower" className="text-[#1E4841] hover:underline font-medium">
                                    Login here
                                </Link>
                            </p>
                        </form>
                    </Form>
                </div>

                {/* Right side */}
                <div className="hidden lg:block lg:w-1/2 xl:w-1/2 relative">
                    <Image 
                        src="/(auth)/login/office.png" 
                        alt="Office" 
                        fill 
                        className="object-cover w-auto h-full" 
                        style={{ objectFit: 'cover' }}
                        sizes="(max-width: 1024px) 0vw, 50vw"
                    />
                    <div className="absolute inset-0 bg-[#1E48419E] rounded-3xl h-3/5 w-4/5 z-10 m-auto" />
                </div>
            </div>
        </div>
    );
}