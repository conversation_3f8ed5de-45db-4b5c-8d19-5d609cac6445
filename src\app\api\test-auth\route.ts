import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    console.log('Testing authentication for:', email);
    
    const result = await DataService.authenticateUser(email, password);
    
    return NextResponse.json({
      success: true,
      result: result,
      hasUser: !!result.user,
      error: result.error
    });
  } catch (error) {
    console.error('Test auth error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}