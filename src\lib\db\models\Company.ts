import { Schema } from 'mongoose';
import { createModel } from '../utils';

const CompanySchema = new Schema({
  name: { type: String, required: true },
  industry: { type: String },
  size: { 
    type: String, 
    enum: ['Small', 'Medium', 'Large', 'Enterprise'],
  },
  logo: { type: String },
  website: { type: String },
  address: {
    street: { type: String },
    city: { type: String },
    state: { type: String },
    zipCode: { type: String },
    country: { type: String }
  },
  contactEmail: { type: String },
  contactPhone: { type: String },
  subscriptionStatus: { 
    type: String, 
    enum: ['Active', 'Pending', 'Expired', 'Cancelled'],
    default: 'Pending'
  },
  subscriptionPlan: { type: Schema.Types.ObjectId, ref: 'PricingPlan' },
  subscriptionStartDate: { type: Date },
  subscriptionEndDate: { type: Date },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
});

// Use the utility function to create the model safely
const Company = createModel('Company', CompanySchema);

export default Company;