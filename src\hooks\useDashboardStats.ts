"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { apiClient } from '@/lib/api/client';

interface DashboardStats {
  totalReports: number;
  newReports: number;
  underReviewReports: number;
  awaitingResponseReports: number;
  resolvedReports: number;
  highPriorityReports: number;
  periodComparison: {
    totalReportsChange: number;
    newReportsChange: number;
    resolvedReportsChange: number;
    period: string;
  };
  chartData?: {
    overTime: Array<{ month: string; reports: number; cases: number }>;
    statusDistribution: Array<{ name: string; value: number; fill: string }>;
  };
  lastCalculated: Date;
}

interface UseDashboardStatsReturn {
  stats: DashboardStats | null;
  isLoading: boolean;
  error: string | null;
  refresh: () => void;
}

export function useDashboardStats(): UseDashboardStatsReturn {
  const { user, isAuthenticated } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardStats = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setStats(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const data = await apiClient.get('/api/dashboard/stats');
      
      if (data.success) {
        setStats(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch dashboard stats');
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch dashboard stats');
      
      // Fallback to default stats
      setStats({
        totalReports: 0,
        newReports: 0,
        underReviewReports: 0,
        awaitingResponseReports: 0,
        resolvedReports: 0,
        highPriorityReports: 0,
        periodComparison: {
          totalReportsChange: 0,
          newReportsChange: 0,
          resolvedReportsChange: 0,
          period: 'month'
        },
        chartData: {
          overTime: [],
          statusDistribution: []
        },
        lastCalculated: new Date()
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  // Fetch stats on mount and when dependencies change
  useEffect(() => {
    fetchDashboardStats();
  }, [fetchDashboardStats]);

  // Set up polling to refresh stats periodically
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(fetchDashboardStats, 300000); // Refresh every 5 minutes
    return () => clearInterval(interval);
  }, [isAuthenticated, fetchDashboardStats]);

  const refresh = useCallback(() => {
    fetchDashboardStats();
  }, [fetchDashboardStats]);

  return {
    stats,
    isLoading,
    error,
    refresh
  };
}