import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    // Simple test authentication - accept any credentials
    if (email && password) {
      // Generate JWT token
      const token = jwt.sign(
        { 
          userId: 'test-user-id',
          email: email,
          role: 'whistleblower',
          companyId: 'test-company-id'
        },
        process.env.JWT_SECRET || 'fallback-secret-key',
        { expiresIn: '24h' }
      );
      
      return NextResponse.json({
        success: true,
        message: 'Test login successful',
        token,
        user: {
          id: 'test-user-id',
          email: email,
          firstName: 'Test',
          lastName: 'User',
          fullName: 'Test User',
          role: 'whistleblower',
          companyId: 'test-company-id',
          isActive: true,
          lastLogin: new Date()
        }
      });
    }
    
    return NextResponse.json(
      { success: false, error: 'Email and password required' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Test login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}