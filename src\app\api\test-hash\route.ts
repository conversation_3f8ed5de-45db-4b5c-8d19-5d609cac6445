import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcrypt';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json();
    
    console.log('Hashing password:', password);
    
    const saltRounds = 12;
    const newHash = await bcrypt.hash(password, saltRounds);
    
    console.log('New hash:', newHash);
    
    // Test the new hash
    const isValid = await bcrypt.compare(password, newHash);
    
    console.log('New hash validation:', isValid);
    
    return NextResponse.json({
      success: true,
      data: {
        password: password,
        newHash: newHash,
        hashLength: newHash.length,
        selfValidation: isValid
      }
    });
  } catch (error) {
    console.error('Test hash API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}