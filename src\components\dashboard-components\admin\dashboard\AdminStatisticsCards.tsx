"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, AlertTriangle, Users, Shield } from "lucide-react";
import { useDashboardStats } from "@/hooks/useDashboardStats";

export default function AdminStatisticsCards() {
    const { stats, isLoading, error } = useDashboardStats();

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[...Array(4)].map((_, index) => (
                    <Card key={index} className="bg-white border-0 shadow-sm animate-pulse">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <div className="h-4 bg-gray-200 rounded w-20"></div>
                            <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="h-8 bg-gray-200 rounded mb-1"></div>
                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        );
    }

    if (error || !stats) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="bg-white border-0 shadow-sm col-span-full">
                    <CardContent className="p-6 text-center">
                        <p className="text-red-500">Failed to load admin statistics</p>
                        {error && <p className="text-sm text-gray-500 mt-1">{error}</p>}
                    </CardContent>
                </Card>
            </div>
        );
    }

    const adminStats = [
        {
            title: "Total Cases",
            value: stats.totalReports,
            icon: TrendingUp,
            color: "text-blue-600",
            bgColor: "bg-blue-50",
            details: [
                { label: "New", value: stats.newReports, color: "bg-green-100 text-green-800" },
                { label: "Under Review", value: stats.underReviewReports, color: "bg-yellow-100 text-yellow-800" },
                { label: "Resolved", value: stats.resolvedReports, color: "bg-gray-100 text-gray-800" }
            ]
        },
        {
            title: "High Priority",
            value: stats.highPriorityReports,
            icon: AlertTriangle,
            color: "text-red-600",
            bgColor: "bg-red-50",
            subtitle: `${stats.totalReports > 0 ? Math.round((stats.highPriorityReports / stats.totalReports) * 100) : 0}% of cases`
        },
        {
            title: "Awaiting Response",
            value: stats.awaitingResponseReports,
            icon: Users,
            color: "text-purple-600",
            bgColor: "bg-purple-50",
            subtitle: `Pending user response`
        },
        {
            title: "Monthly Growth",
            value: `${stats.periodComparison.totalReportsChange >= 0 ? '+' : ''}${stats.periodComparison.totalReportsChange}%`,
            icon: Shield,
            color: stats.periodComparison.totalReportsChange >= 0 ? "text-green-600" : "text-red-600",
            bgColor: stats.periodComparison.totalReportsChange >= 0 ? "bg-green-50" : "bg-red-50",
            subtitle: `Compared to last month`
        }
    ];

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {adminStats.map((stat, index) => (
                <Card key={index} className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">
                            {stat.title}
                        </CardTitle>
                        <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                            <stat.icon className={`h-4 w-4 ${stat.color}`} />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-gray-900 mb-1">
                            {stat.value}
                        </div>
                        {stat.subtitle && (
                            <p className="text-xs text-gray-500">
                                {stat.subtitle}
                            </p>
                        )}
                        {stat.details && (
                            <div className="flex flex-wrap gap-1 mt-2">
                                {stat.details.map((detail, idx) => (
                                    <Badge 
                                        key={idx} 
                                        variant="secondary" 
                                        className={`text-xs ${detail.color}`}
                                    >
                                        {detail.label}: {detail.value}
                                    </Badge>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}