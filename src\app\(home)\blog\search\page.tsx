"use client";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState, useEffect, Suspense } from "react";
import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search, ArrowLeft, X, ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Card, CardHeader, CardContent, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ALL_BLOG_POSTS } from "@/lib/mockData/blogData";
import { BlogCard } from "@/lib/types";

// Search results blog card component
const SearchBlogCard = ({ card }: { card: BlogCard }) => (
  <Card className="flex flex-col hover:shadow-xl transition-shadow duration-300 pt-0 h-fit w-full">
    <CardHeader className="overflow-hidden p-0 rounded-2xl">
      <div className="aspect-[16/9] w-full overflow-hidden">
        <Image
          src={card.image}
          alt="Blog post image"
          width={400}
          height={225}
          className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
        />
      </div>
    </CardHeader>
    <CardContent className="space-y-3 sm:space-y-4 p-3 sm:p-4 md:p-5">
      <div className="flex flex-wrap justify-between items-center gap-2">
        <p className="text-xs font-medium text-[#1E4841] rounded-4xl py-1 px-2 bg-[#ECF4E9]">{card.category}</p>
        <p className="text-xs text-[#1E4841]">{card.date}</p>
      </div>
      <CardTitle className="text-lg sm:text-xl font-bold text-[#242E2C] hover:text-[#1E4841] transition-colors duration-300 line-clamp-2">
        {card.title}
      </CardTitle>
      <CardDescription className="text-[#4B5563] leading-relaxed text-sm sm:text-base line-clamp-2 sm:line-clamp-3">
        {card.description}
      </CardDescription>
    </CardContent>
    <CardFooter className="flex flex-col gap-3 sm:gap-4 items-start p-3 sm:p-4 md:p-5 pt-0 sm:pt-0 md:pt-0">
      <div className="flex items-center justify-between w-full flex-wrap gap-2">
        <div className="flex items-center gap-1 sm:gap-2">
          <Avatar>
            <AvatarImage src={card.author.image} alt={card.author.name} />
            <AvatarFallback>{card.author.initials}</AvatarFallback>
          </Avatar>
          <p className="text-xs text-[#4B5563] line-clamp-1">{card.author.name}</p>
        </div>
        <p className="text-[#4B5563] text-xs">{card.readTime}</p>
      </div>
      <div className="w-full">
        <Link
          href={`/blog/${card.id}`}
          aria-label={`Read more about ${card.title}`}
          className="group w-full hover:bg-[#ECF4E9] rounded-lg"
        >
          <div className="flex items-center rounded-lg transition-all duration-300 gap-2">
            <p className="text-[#1E4841] font-medium">Read More</p>
            <ArrowRight className="h-4 w-4 text-[#1E4841] transition-transform duration-300 group-hover:translate-x-2" />
          </div>
        </Link>
      </div>
    </CardFooter>
  </Card>
);

function SearchResultsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const query = searchParams.get("q") || "";
  const tag = searchParams.get("tag") || "";
  const [searchQuery, setSearchQuery] = useState(query);
  const [searchResults, setSearchResults] = useState<BlogCard[]>([]);
  
  useEffect(() => {
    // Filter cards based on search query or tag
    const filteredCards = ALL_BLOG_POSTS.filter(card => {
      if (tag) {
        return card.tags?.includes(tag);
      }
      
      if (query) {
        return card.title.toLowerCase().includes(query.toLowerCase()) || 
          card.description.toLowerCase().includes(query.toLowerCase()) ||
          card.author.name.toLowerCase().includes(query.toLowerCase()) ||
          card.category.toLowerCase().includes(query.toLowerCase()) ||
          card.tags?.some(t => t.toLowerCase().includes(query.toLowerCase()));
      }
      
      return false;
    });
    
    setSearchResults(filteredCards);
  }, [query, tag]);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Update URL with new search query
    router.push(`/blog/search?q=${encodeURIComponent(searchQuery)}`, { scroll: false });
    
    // Re-filter results
    const filteredCards = ALL_BLOG_POSTS.filter(card => 
      card.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      card.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      card.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      card.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      card.tags?.some(t => t.toLowerCase().includes(searchQuery.toLowerCase()))
    );
    
    setSearchResults(filteredCards);
  };
  
  return (
    <section className="px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px] py-6 sm:py-8 mx-auto">
      <div className="mb-8">
        <Link href="/blog" className="flex items-center text-[#1E4841] hover:underline mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Blog
        </Link>
        <h1 className="text-2xl sm:text-2xl md:text-3xl font-bold text-[#1E4841] mb-4 sm:mb-6">
          {tag ? `Articles tagged with "${tag}"` : 
           query ? `Search Results for "${query}"` : 
           "Search Results"}
        </h1>
        
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-2 mb-6 sm:mb-8">
          <div className="relative flex-1 mb-2 sm:mb-0">
            <Input
              type="text"
              placeholder="Search articles, topics, or authors..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-4 sm:py-6 pl-12 pr-10 text-gray-900 bg-white border border-gray-300 rounded-lg"
            />
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            {searchQuery && (
              <button
                type="button"
                onClick={() => setSearchQuery('')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                aria-label="Clear search"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
          <Button 
            type="submit"
            className="px-4 py-4 sm:px-6 sm:py-6 text-[#1E4841] bg-[#BBF49C] border border-[#BBF49C] hover:bg-lime-200 w-full sm:w-auto"
          >
            Search
          </Button>
        </form>
      </div>
      
      {searchResults.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {searchResults.map((card, index) => (
            <SearchBlogCard key={index} card={card} />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 sm:py-16">
          <p className="text-xl text-[#1E4841] font-semibold mb-2">No results found</p>
          <p className="text-gray-500">Try different keywords or browse all articles</p>
          <Link href="/blog">
            <Button 
              className="mt-6 px-6 py-2 text-[#1E4841] bg-[#BBF49C] border border-[#BBF49C] hover:bg-lime-200"
            >
              Browse All Articles
            </Button>
          </Link>
        </div>
      )}
    </section>
  );
}

export default function SearchResults() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 pt-20">
        <Suspense fallback={
          <div className="px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px] py-6 sm:py-8 mx-auto">
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
              <div className="h-12 bg-gray-200 rounded w-3/4"></div>
              <div className="h-12 bg-gray-200 rounded w-full"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-gray-200 rounded-lg h-64"></div>
                ))}
              </div>
            </div>
          </div>
        }>
          <SearchResultsContent />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}